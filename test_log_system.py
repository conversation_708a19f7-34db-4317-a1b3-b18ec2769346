#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FlipTalk AI日志系统
"""

import sys
import os
import time
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_logger_basic():
    """测试基础日志功能"""
    print("🧪 测试基础日志功能...")
    
    try:
        from core.logger import get_logger, LogLevel, log_info, log_warning, log_error, log_debug
        
        logger = get_logger()
        
        # 测试不同级别的日志
        log_debug("TestModule", "这是一条调试信息")
        log_info("TestModule", "这是一条信息日志")
        log_warning("TestModule", "这是一条警告日志")
        log_error("TestModule", "这是一条错误日志")
        
        # 检查日志是否被记录
        logs = logger.get_logs()
        if len(logs) >= 4:
            print("✅ 基础日志记录功能正常")
            return True
        else:
            print(f"❌ 日志记录不完整，期望4条，实际{len(logs)}条")
            return False
            
    except Exception as e:
        print(f"❌ 基础日志功能测试失败: {e}")
        return False

def test_logger_filtering():
    """测试日志过滤功能"""
    print("\n🧪 测试日志过滤功能...")
    
    try:
        from core.logger import get_logger, LogLevel
        
        logger = get_logger()
        
        # 禁用DEBUG级别
        logger.set_level_enabled(LogLevel.DEBUG, False)
        
        # 添加各级别日志
        logger.add_log(LogLevel.DEBUG, "FilterTest", "调试信息（应被过滤）")
        logger.add_log(LogLevel.INFO, "FilterTest", "信息日志")
        logger.add_log(LogLevel.WARNING, "FilterTest", "警告日志")
        logger.add_log(LogLevel.ERROR, "FilterTest", "错误日志")
        
        # 检查过滤结果
        all_logs = logger.get_logs()
        info_logs = logger.get_logs([LogLevel.INFO, LogLevel.WARNING, LogLevel.ERROR])
        
        print(f"  总日志数: {len(all_logs)}")
        print(f"  过滤后日志数: {len(info_logs)}")
        
        # 重新启用DEBUG级别
        logger.set_level_enabled(LogLevel.DEBUG, True)
        
        print("✅ 日志过滤功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 日志过滤功能测试失败: {e}")
        return False

def test_logger_export():
    """测试日志导出功能"""
    print("\n🧪 测试日志导出功能...")
    
    try:
        from core.logger import get_logger, LogLevel
        
        logger = get_logger()
        
        # 添加测试日志
        logger.add_log(LogLevel.INFO, "ExportTest", "导出测试日志1")
        logger.add_log(LogLevel.WARNING, "ExportTest", "导出测试日志2")
        logger.add_log(LogLevel.ERROR, "ExportTest", "导出测试日志3")
        
        # 导出到临时文件
        export_path = "temp_test_logs.txt"
        success = logger.export_logs(export_path)
        
        if success and os.path.exists(export_path):
            # 检查文件内容
            with open(export_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "ExportTest" in content and "导出测试日志" in content:
                print("✅ 日志导出功能正常")
                # 清理临时文件
                os.remove(export_path)
                return True
            else:
                print("❌ 导出文件内容不正确")
                return False
        else:
            print("❌ 日志导出失败")
            return False
            
    except Exception as e:
        print(f"❌ 日志导出功能测试失败: {e}")
        return False

def test_logger_stats():
    """测试日志统计功能"""
    print("\n🧪 测试日志统计功能...")
    
    try:
        from core.logger import get_logger, LogLevel
        
        logger = get_logger()
        
        # 清除现有日志
        logger.clear_logs()
        
        # 添加不同级别的日志
        logger.add_log(LogLevel.INFO, "StatsTest", "信息1")
        logger.add_log(LogLevel.INFO, "StatsTest", "信息2")
        logger.add_log(LogLevel.WARNING, "StatsTest", "警告1")
        logger.add_log(LogLevel.ERROR, "StatsTest", "错误1")
        
        # 获取统计信息
        stats = logger.get_stats()
        
        expected_stats = {
            'INFO': 2,
            'WARNING': 1,
            'ERROR': 1,
            'DEBUG': 0,
            'TOTAL': 4
        }
        
        success = True
        for level, expected_count in expected_stats.items():
            actual_count = stats.get(level, 0)
            if actual_count != expected_count:
                print(f"❌ {level}级别统计错误: 期望{expected_count}, 实际{actual_count}")
                success = False
            else:
                print(f"  ✅ {level}: {actual_count} 条")
        
        if success:
            print("✅ 日志统计功能正常")
        
        return success
        
    except Exception as e:
        print(f"❌ 日志统计功能测试失败: {e}")
        return False

def test_module_logger():
    """测试模块日志记录器"""
    print("\n🧪 测试模块日志记录器...")
    
    try:
        from core.logger import create_module_logger, get_logger
        
        # 创建模块日志记录器
        module_logger = create_module_logger("TestModule")
        
        # 记录不同级别的日志
        module_logger.debug("模块调试信息")
        module_logger.info("模块信息日志")
        module_logger.warning("模块警告日志")
        module_logger.error("模块错误日志")
        
        # 检查日志是否正确记录
        logger = get_logger()
        logs = logger.get_logs()
        
        # 查找包含TestModule的日志
        test_logs = [log for log in logs if log.module == "TestModule"]
        
        if len(test_logs) >= 4:
            print("✅ 模块日志记录器功能正常")
            return True
        else:
            print(f"❌ 模块日志记录不完整，期望4条，实际{len(test_logs)}条")
            return False
            
    except Exception as e:
        print(f"❌ 模块日志记录器测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n🧪 测试UI组件...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.log_area import LogArea, LogDisplayWidget, LogFilterWidget, LogToolbarWidget
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试各个组件的创建
        components = {
            "LogArea": LogArea,
            "LogDisplayWidget": LogDisplayWidget,
            "LogFilterWidget": LogFilterWidget,
            "LogToolbarWidget": LogToolbarWidget
        }
        
        success = True
        for name, component_class in components.items():
            try:
                component = component_class()
                print(f"  ✅ {name}: 创建成功")
            except Exception as e:
                print(f"  ❌ {name}: 创建失败 - {e}")
                success = False
        
        if success:
            print("✅ UI组件测试通过")
        
        return success
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def test_thread_safety():
    """测试线程安全性"""
    print("\n🧪 测试线程安全性...")
    
    try:
        from core.logger import get_logger, LogLevel
        import threading
        import time
        
        logger = get_logger()
        logger.clear_logs()
        
        # 多线程写入日志
        def write_logs(thread_id):
            for i in range(10):
                logger.add_log(LogLevel.INFO, f"Thread{thread_id}", f"消息{i}")
                time.sleep(0.001)  # 短暂延迟
        
        # 创建多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=write_logs, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查日志数量
        logs = logger.get_logs()
        expected_count = 5 * 10  # 5个线程，每个10条日志
        
        if len(logs) == expected_count:
            print(f"✅ 线程安全测试通过，记录了{len(logs)}条日志")
            return True
        else:
            print(f"❌ 线程安全测试失败，期望{expected_count}条，实际{len(logs)}条")
            return False
            
    except Exception as e:
        print(f"❌ 线程安全测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI 日志系统测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("基础日志功能", test_logger_basic),
        ("日志过滤功能", test_logger_filtering),
        ("日志导出功能", test_logger_export),
        ("日志统计功能", test_logger_stats),
        ("模块日志记录器", test_module_logger),
        ("UI组件", test_ui_components),
        ("线程安全性", test_thread_safety)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 日志系统完全正常！")
        print("\n💡 功能特点:")
        print("1. ✅ 分级日志系统 (ERROR/WARNING/INFO/DEBUG)")
        print("2. ✅ 颜色显示支持 (深色主题适配)")
        print("3. ✅ 日志过滤和搜索")
        print("4. ✅ 日志导出功能")
        print("5. ✅ 统计信息显示")
        print("6. ✅ 线程安全设计")
        print("7. ✅ 模块化日志记录器")
        print("8. ✅ 完整的UI组件")
        
        print("\n🎯 使用方法:")
        print("1. 启动FlipTalk AI应用")
        print("2. 点击导航栏的'运行日志'")
        print("3. 查看实时日志输出")
        print("4. 使用过滤器筛选日志级别")
        print("5. 使用搜索功能查找特定内容")
        print("6. 导出日志到文件")
        
    else:
        print(f"\n❌ 日志系统有 {total_tests - passed_tests} 个问题需要修复")

if __name__ == "__main__":
    main()
