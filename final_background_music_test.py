#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终背景音乐功能测试
使用实际文件进行完整的混合和视频合成测试
"""

import os
import sys
import subprocess
import datetime

def test_complete_background_music_workflow():
    """测试完整的背景音乐工作流程"""
    print("🚀 最终背景音乐功能测试")
    print("=" * 60)
    
    # 实际文件路径
    background_file = r"J:\MyAi\03 FlipTalk Ai\output\voice_separation\Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav"
    voiceover_file = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\combined_voiceover.wav"
    output_dir = r"J:\MyAi\03 FlipTalk Ai\output\synthesis"
    
    print("📋 第一步: 验证输入文件")
    
    # 检查文件存在性
    if not os.path.exists(background_file):
        print(f"❌ 背景音乐文件不存在: {background_file}")
        return False
    
    if not os.path.exists(voiceover_file):
        print(f"❌ 配音文件不存在: {voiceover_file}")
        return False
    
    print("✅ 所有输入文件存在")
    
    # 分析文件
    try:
        from pydub import AudioSegment
        
        bg_audio = AudioSegment.from_file(background_file)
        vo_audio = AudioSegment.from_wav(voiceover_file)
        
        print(f"📊 背景音乐: {bg_audio.dBFS:.1f}dB, {len(bg_audio)/1000:.1f}s")
        print(f"📊 配音音频: {vo_audio.dBFS:.1f}dB, {len(vo_audio)/1000:.1f}s")
        
    except Exception as e:
        print(f"❌ 分析音频文件失败: {e}")
        return False
    
    print("\n📋 第二步: 执行背景音乐混合")
    
    # 执行混合
    mixed_audio_path = mix_background_music_enhanced(background_file, voiceover_file, output_dir)
    
    if not mixed_audio_path:
        print("❌ 背景音乐混合失败")
        return False
    
    print(f"✅ 背景音乐混合成功: {mixed_audio_path}")
    
    print("\n📋 第三步: 创建测试视频")
    
    # 创建测试视频
    test_video_path = create_test_video_with_background_music(mixed_audio_path, output_dir)
    
    if not test_video_path:
        print("❌ 测试视频创建失败")
        return False
    
    print(f"✅ 测试视频创建成功: {test_video_path}")
    
    print("\n📋 第四步: 验证最终结果")
    
    # 验证结果
    success = verify_background_music_in_video(test_video_path)
    
    return success

def mix_background_music_enhanced(background_file, voiceover_file, output_dir):
    """增强版背景音乐混合"""
    try:
        from pydub import AudioSegment
        import datetime
        
        print("🎵 开始增强版背景音乐混合...")
        
        # 加载音频
        voiceover_audio = AudioSegment.from_wav(voiceover_file)
        background_music = AudioSegment.from_file(background_file)
        
        print(f"📊 原始音量 - 配音: {voiceover_audio.dBFS:.1f}dB, 背景音乐: {background_music.dBFS:.1f}dB")
        
        # 调整长度
        voiceover_duration = len(voiceover_audio)
        if len(background_music) > voiceover_duration:
            background_music = background_music[:voiceover_duration]
        elif len(background_music) < voiceover_duration:
            repeat_times = int(voiceover_duration / len(background_music)) + 1
            background_music = background_music * repeat_times
            background_music = background_music[:voiceover_duration]
        
        # 应用修复后的音量调整
        target_bg_db = voiceover_audio.dBFS - 7  # 背景音乐比配音低7dB
        gain_adjustment = target_bg_db - background_music.dBFS
        background_music = background_music + gain_adjustment
        
        print(f"🔧 音量调整: {gain_adjustment:+.1f}dB")
        print(f"📊 调整后音量 - 配音: {voiceover_audio.dBFS:.1f}dB, 背景音乐: {background_music.dBFS:.1f}dB")
        
        # 混合音频
        mixed_audio = voiceover_audio.overlay(background_music)
        
        # 音量优化
        if mixed_audio.dBFS > -3:
            mixed_audio = mixed_audio + (-3 - mixed_audio.dBFS)
        elif mixed_audio.dBFS < -18:
            mixed_audio = mixed_audio + (-15 - mixed_audio.dBFS)
        
        # 导出
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        mixed_filename = f"final_test_mixed_audio_{timestamp}.wav"
        mixed_audio_path = os.path.join(output_dir, mixed_filename)
        
        mixed_audio.export(mixed_audio_path, format="wav")
        
        print(f"✅ 混合完成: 最终音量={mixed_audio.dBFS:.1f}dB")
        
        return mixed_audio_path
        
    except Exception as e:
        print(f"❌ 混合失败: {e}")
        return None

def create_test_video_with_background_music(audio_path, output_dir):
    """使用混合音频创建测试视频"""
    try:
        print("🎬 创建测试视频...")
        
        # 查找原始视频文件
        video_files = []
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    video_files.append(os.path.join(root, file))
        
        if not video_files:
            print("❌ 未找到视频文件")
            return None
        
        input_video = video_files[0]
        print(f"📁 使用视频文件: {input_video}")
        
        # 生成输出文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"final_test_with_background_music_{timestamp}.mp4"
        output_video_path = os.path.join(output_dir, output_filename)
        
        # FFmpeg命令
        cmd = [
            "ffmpeg",
            "-i", input_video,
            "-i", audio_path,
            "-c:v", "copy",
            "-c:a", "aac",
            "-b:a", "192k",
            "-ar", "44100",
            "-ac", "2",
            "-map", "0:v:0",
            "-map", "1:a:0",
            "-shortest",
            "-avoid_negative_ts", "make_zero",
            "-y",
            output_video_path
        ]
        
        print(f"🔧 执行FFmpeg...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ 视频创建成功")
            return output_video_path
        else:
            print(f"❌ FFmpeg失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 创建视频失败: {e}")
        return None

def verify_background_music_in_video(video_path):
    """验证视频中是否包含背景音乐"""
    try:
        print("🔍 验证视频中的背景音乐...")
        
        # 从视频提取音频
        extracted_audio_path = video_path.replace('.mp4', '_extracted_audio.wav')
        
        cmd = [
            "ffmpeg",
            "-i", video_path,
            "-vn",
            "-acodec", "pcm_s16le",
            "-ar", "44100",
            "-ac", "2",
            "-y",
            extracted_audio_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            print(f"❌ 音频提取失败: {result.stderr}")
            return False
        
        # 分析提取的音频
        from pydub import AudioSegment
        extracted_audio = AudioSegment.from_wav(extracted_audio_path)
        
        print(f"📊 提取的音频: {extracted_audio.dBFS:.1f}dB, {len(extracted_audio)/1000:.1f}s")
        
        # 与原始配音比较
        voiceover_file = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\combined_voiceover.wav"
        voiceover_audio = AudioSegment.from_wav(voiceover_file)
        
        print(f"📊 原始配音: {voiceover_audio.dBFS:.1f}dB")
        
        # 如果提取的音频音量明显高于原始配音，说明包含了背景音乐
        volume_difference = extracted_audio.dBFS - voiceover_audio.dBFS
        print(f"📊 音量差异: {volume_difference:+.1f}dB")
        
        if volume_difference > 1:  # 如果混合后音量明显更高
            print("✅ 检测到背景音乐！音量提升表明包含了背景音乐")
            return True
        else:
            print("❌ 未检测到明显的背景音乐效果")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    success = test_complete_background_music_workflow()
    
    print("\n" + "=" * 60)
    print("🔍 最终测试结果:")
    
    if success:
        print("✅ 背景音乐功能完全正常！")
        print("\n💡 如果您的实际操作中仍然没有背景音乐，请:")
        print("1. 确保在UI中勾选了'保留背景音乐'选项")
        print("2. 重新生成视频，查看控制台输出")
        print("3. 检查synthesis目录中是否有mixed_audio_with_background_*.wav文件")
        print("4. 使用本测试生成的视频文件验证效果")
    else:
        print("❌ 背景音乐功能仍有问题")
        print("\n💡 请检查:")
        print("1. 所有依赖库是否正确安装")
        print("2. 文件权限是否正确")
        print("3. FFmpeg是否正确配置")

if __name__ == "__main__":
    main()
