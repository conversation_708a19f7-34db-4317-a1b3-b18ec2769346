#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepL API实际翻译测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_actual_translation():
    """测试实际翻译功能"""
    print("🌐 测试DeepL实际翻译功能...")
    
    try:
        from plugins.subtitle_translator.plugin import DeepLTranslator
        from core.config_manager import get_config_manager
        
        # 从配置加载API密钥
        config_manager = get_config_manager()
        api_key = config_manager.get("api_keys.deepl_api_key", "")
        
        if not api_key:
            print("❌ 未找到DeepL API密钥，请先在API设置中配置")
            return False
        
        print(f"🔑 使用API密钥: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
        
        # 创建翻译器
        translator = DeepLTranslator(api_key)
        
        # 测试翻译
        test_texts = [
            "Hello, how are you?",
            "This is a test sentence.",
            "Machine translation is amazing!"
        ]
        
        print("\n📝 开始翻译测试...")
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n🧪 测试 {i}: {text}")
            
            try:
                # 英文翻译成中文
                result = translator.translate_text(text, "EN", "ZH")
                
                if result:
                    print(f"✅ 翻译成功: {result}")
                else:
                    print("❌ 翻译失败: 返回空结果")
                    return False
                    
            except Exception as e:
                print(f"❌ 翻译异常: {e}")
                return False
        
        print("\n🎉 所有翻译测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 翻译测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_plugin_workflow():
    """测试翻译插件完整工作流程"""
    print("\n🔄 测试翻译插件完整工作流程...")
    
    try:
        from plugins.subtitle_translator.plugin import SubtitleTranslatorPlugin
        from core.config_manager import get_config_manager
        
        # 从配置加载API密钥
        config_manager = get_config_manager()
        api_key = config_manager.get("api_keys.deepl_api_key", "")
        
        if not api_key:
            print("❌ 未找到DeepL API密钥")
            return False
        
        # 创建插件
        plugin = SubtitleTranslatorPlugin()
        
        # 设置翻译器
        success = plugin.set_translator("deepl", api_key)
        if not success:
            print("❌ 设置翻译器失败")
            return False
        
        print("✅ 翻译器设置成功")
        
        # 测试翻译
        test_texts = [
            "Welcome to FlipTalk AI",
            "This is a subtitle translation test"
        ]
        
        print("\n📝 测试插件翻译功能...")
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n🧪 插件测试 {i}: {text}")
            
            try:
                result = plugin.translate_text(text, "EN", "ZH")
                
                if result:
                    print(f"✅ 插件翻译成功: {result}")
                else:
                    print("❌ 插件翻译失败")
                    return False
                    
            except Exception as e:
                print(f"❌ 插件翻译异常: {e}")
                return False
        
        print("\n🎉 插件工作流程测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 插件工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenarios():
    """测试错误场景"""
    print("\n⚠️ 测试错误场景...")
    
    try:
        from plugins.subtitle_translator.plugin import DeepLTranslator
        
        # 测试无效API密钥的实际请求
        print("🧪 测试无效API密钥...")
        invalid_translator = DeepLTranslator("invalid-api-key-12345")
        
        result = invalid_translator.translate_text("Hello", "EN", "ZH")
        
        if result is None:
            print("✅ 无效API密钥正确返回None")
        else:
            print(f"❌ 无效API密钥应该返回None，但返回了: {result}")
            return False
        
        print("✅ 错误场景测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI DeepL API实际翻译测试")
    print("=" * 60)
    
    print("⚠️ 注意: 此测试将发送实际的API请求")
    print("⚠️ 请确保:")
    print("1. 已在API设置中配置了有效的DeepL API密钥")
    print("2. 网络连接正常")
    print("3. API配额充足")
    
    # 询问用户是否继续
    try:
        user_input = input("\n是否继续进行实际翻译测试？(y/N): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            print("❌ 用户取消测试")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断测试")
        return
    
    # 运行测试
    tests = [
        ("实际翻译功能", test_actual_translation),
        ("翻译插件工作流程", test_translation_plugin_workflow),
        ("错误场景", test_error_scenarios)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*15} {test_name} {'='*15}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 实际翻译测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 DeepL API实际翻译功能完全正常！")
        
        print("\n✅ 验证通过的功能:")
        print("1. ✅ DeepL API密钥配置正确")
        print("2. ✅ 网络连接和API端点正常")
        print("3. ✅ 翻译请求和响应处理正确")
        print("4. ✅ 错误处理机制有效")
        print("5. ✅ 插件集成工作正常")
        
        print("\n🎯 现在可以正常使用:")
        print("• 在API设置界面配置DeepL密钥")
        print("• 在字幕翻译对话框选择DeepL翻译器")
        print("• 密钥会自动填充，直接开始翻译")
        print("• 享受高质量的DeepL翻译服务")
        
    else:
        print(f"\n❌ 仍有 {total_tests - passed_tests} 个问题")
        
        print("\n🔧 可能的原因:")
        print("• API密钥无效或已过期")
        print("• 网络连接问题")
        print("• API配额不足")
        print("• DeepL服务暂时不可用")
        
        print("\n💡 解决建议:")
        print("• 检查API密钥是否正确")
        print("• 确认网络连接正常")
        print("• 查看DeepL账户配额状态")
        print("• 查看控制台详细错误信息")

if __name__ == "__main__":
    main()
