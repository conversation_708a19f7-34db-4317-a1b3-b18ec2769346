#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频音量分析和修复工具
专门用于分析和修复人声分离后的音量问题
"""

import os
import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

class AudioVolumeAnalyzer:
    """音频音量分析器"""
    
    def __init__(self):
        self.supported_formats = ['.wav', '.mp3', '.m4a', '.flac', '.aac', '.ogg']
    
    def analyze_audio_file(self, file_path):
        """
        分析音频文件的音量信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            dict: 音频分析结果
        """
        try:
            import librosa
            import soundfile as sf
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"音频文件不存在: {file_path}")
            
            # 加载音频文件
            audio_data, sample_rate = librosa.load(file_path, sr=None, mono=False)
            
            # 计算音量统计信息
            if audio_data.ndim == 1:
                # 单声道
                rms = np.sqrt(np.mean(audio_data ** 2))
                peak = np.max(np.abs(audio_data))
                duration = len(audio_data) / sample_rate
                channels = 1
            else:
                # 立体声或多声道
                rms = np.sqrt(np.mean(audio_data ** 2))
                peak = np.max(np.abs(audio_data))
                duration = audio_data.shape[1] / sample_rate
                channels = audio_data.shape[0]
            
            # 转换为dB
            rms_db = 20 * np.log10(rms + 1e-10)
            peak_db = 20 * np.log10(peak + 1e-10)
            
            # 计算动态范围
            dynamic_range = peak_db - rms_db
            
            # 计算响度单位 (LUFS 近似)
            lufs_approx = rms_db - 23  # 简化的LUFS计算
            
            # 文件信息
            file_size = os.path.getsize(file_path)
            
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size_mb': file_size / (1024 * 1024),
                'sample_rate': sample_rate,
                'duration': duration,
                'channels': channels,
                'rms_db': rms_db,
                'peak_db': peak_db,
                'dynamic_range': dynamic_range,
                'lufs_approx': lufs_approx,
                'is_clipped': peak >= 0.99,
                'is_too_quiet': rms_db < -30,
                'is_too_loud': rms_db > -6,
                'quality_score': self._calculate_quality_score(rms_db, peak_db, dynamic_range)
            }
            
        except Exception as e:
            print(f"❌ 分析音频文件失败: {e}")
            return None
    
    def _calculate_quality_score(self, rms_db, peak_db, dynamic_range):
        """
        计算音频质量评分 (0-100)
        
        Args:
            rms_db: RMS音量 (dB)
            peak_db: 峰值音量 (dB)
            dynamic_range: 动态范围 (dB)
            
        Returns:
            int: 质量评分
        """
        score = 100
        
        # RMS音量评分 (理想范围: -18dB 到 -12dB)
        if rms_db < -25:
            score -= 30  # 音量过低
        elif rms_db < -20:
            score -= 15
        elif rms_db > -6:
            score -= 25  # 音量过高
        elif rms_db > -10:
            score -= 10
        
        # 峰值评分 (避免削波)
        if peak_db > -1:
            score -= 20  # 可能削波
        elif peak_db > -3:
            score -= 10
        
        # 动态范围评分 (理想范围: 6dB 到 20dB)
        if dynamic_range < 3:
            score -= 15  # 动态范围过小
        elif dynamic_range > 25:
            score -= 10  # 动态范围过大
        
        return max(0, min(100, score))
    
    def analyze_voice_separation_results(self, output_dir):
        """
        分析人声分离结果的音量情况
        
        Args:
            output_dir: 人声分离输出目录
            
        Returns:
            dict: 分析结果
        """
        results = {
            'original': None,
            'vocal': None,
            'instrumental': None,
            'background': None,
            'analysis_summary': {}
        }
        
        try:
            # 查找音频文件
            audio_files = []
            for ext in self.supported_formats:
                audio_files.extend(Path(output_dir).glob(f"*{ext}"))
            
            for file_path in audio_files:
                file_name = file_path.name.lower()
                analysis = self.analyze_audio_file(str(file_path))
                
                if analysis:
                    if 'vocal' in file_name or 'voice' in file_name:
                        results['vocal'] = analysis
                    elif 'instrumental' in file_name or 'background' in file_name or 'other' in file_name:
                        results['instrumental'] = analysis
                        results['background'] = analysis  # 别名
                    elif 'original' in file_name or 'input' in file_name:
                        results['original'] = analysis
            
            # 生成分析摘要
            results['analysis_summary'] = self._generate_analysis_summary(results)
            
            return results
            
        except Exception as e:
            print(f"❌ 分析人声分离结果失败: {e}")
            return results
    
    def _generate_analysis_summary(self, results):
        """生成分析摘要"""
        summary = {
            'volume_issues': [],
            'quality_issues': [],
            'recommendations': []
        }
        
        # 检查背景音乐音量问题
        if results['background']:
            bg = results['background']
            if bg['is_too_quiet']:
                summary['volume_issues'].append("背景音乐音量过低")
                summary['recommendations'].append("建议增加背景音乐增益补偿")
            
            if bg['quality_score'] < 70:
                summary['quality_issues'].append(f"背景音乐质量评分较低: {bg['quality_score']}")
        
        # 检查人声音量问题
        if results['vocal']:
            vocal = results['vocal']
            if vocal['is_too_quiet']:
                summary['volume_issues'].append("人声音量过低")
                summary['recommendations'].append("建议增加人声增益补偿")
            
            if vocal['is_clipped']:
                summary['volume_issues'].append("人声可能存在削波")
                summary['recommendations'].append("建议降低人声音量或使用限制器")
        
        # 音量平衡检查
        if results['vocal'] and results['background']:
            vocal_db = results['vocal']['rms_db']
            bg_db = results['background']['rms_db']
            volume_diff = vocal_db - bg_db
            
            if volume_diff < 3:
                summary['volume_issues'].append("人声与背景音乐音量差异过小")
                summary['recommendations'].append("建议增加人声音量或降低背景音乐音量")
            elif volume_diff > 15:
                summary['volume_issues'].append("人声与背景音乐音量差异过大")
                summary['recommendations'].append("建议提高背景音乐音量")
        
        return summary
    
    def print_analysis_report(self, results):
        """打印分析报告"""
        print("🔊 音频音量分析报告")
        print("=" * 60)
        
        # 打印各音频文件分析结果
        for audio_type, analysis in results.items():
            if audio_type == 'analysis_summary' or not analysis:
                continue
                
            print(f"\n📊 {audio_type.upper()} 音频分析:")
            print(f"  文件: {analysis['file_name']}")
            print(f"  时长: {analysis['duration']:.2f}s")
            print(f"  采样率: {analysis['sample_rate']}Hz")
            print(f"  声道数: {analysis['channels']}")
            print(f"  RMS音量: {analysis['rms_db']:.1f}dB")
            print(f"  峰值音量: {analysis['peak_db']:.1f}dB")
            print(f"  动态范围: {analysis['dynamic_range']:.1f}dB")
            print(f"  质量评分: {analysis['quality_score']}/100")
            
            # 状态指示
            status_indicators = []
            if analysis['is_too_quiet']:
                status_indicators.append("🔇 音量过低")
            if analysis['is_too_loud']:
                status_indicators.append("🔊 音量过高")
            if analysis['is_clipped']:
                status_indicators.append("⚠️ 可能削波")
            if analysis['quality_score'] >= 80:
                status_indicators.append("✅ 质量良好")
            elif analysis['quality_score'] >= 60:
                status_indicators.append("⚠️ 质量一般")
            else:
                status_indicators.append("❌ 质量较差")
            
            if status_indicators:
                print(f"  状态: {' '.join(status_indicators)}")
        
        # 打印分析摘要
        summary = results.get('analysis_summary', {})
        if summary:
            print(f"\n📋 分析摘要:")
            
            if summary['volume_issues']:
                print(f"  音量问题:")
                for issue in summary['volume_issues']:
                    print(f"    • {issue}")
            
            if summary['quality_issues']:
                print(f"  质量问题:")
                for issue in summary['quality_issues']:
                    print(f"    • {issue}")
            
            if summary['recommendations']:
                print(f"  建议:")
                for rec in summary['recommendations']:
                    print(f"    • {rec}")
            
            if not summary['volume_issues'] and not summary['quality_issues']:
                print(f"  ✅ 音频质量良好，无明显问题")

def main():
    """主函数"""
    print("🚀 FlipTalk AI 音频音量分析工具")
    print("=" * 50)
    
    analyzer = AudioVolumeAnalyzer()
    
    # 分析示例目录
    test_dir = "output/voice_separation"
    
    if os.path.exists(test_dir):
        print(f"📂 分析目录: {test_dir}")
        results = analyzer.analyze_voice_separation_results(test_dir)
        analyzer.print_analysis_report(results)
    else:
        print(f"❌ 测试目录不存在: {test_dir}")
        print("请先执行人声分离操作生成音频文件")
    
    print("\n" + "=" * 50)
    print("💡 音量修复说明:")
    print("• 背景音乐增益补偿: +6dB (CascadedNet) / +8dB (Demucs)")
    print("• 人声增益补偿: +3dB")
    print("• 音频归一化: 目标 -12dB RMS")
    print("• 削波保护: 峰值限制在 0.95")
    print("• 智能音量调整: 根据实际音量动态调整")

if __name__ == "__main__":
    main()
