#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频合成对话框
将多个字幕配音音频片段按照字幕时间戳顺序进行智能合并，并替换原视频的音轨
支持保留背景音乐选项
"""

import os
import sys
import json
import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QTextEdit, QProgressBar, QCheckBox, QComboBox,
    QGroupBox, QGridLayout, QFrame, QScrollArea, QWidget,
    QMessageBox, QSpinBox, QDoubleSpinBox, QTabWidget,
    QListWidget, QListWidgetItem, QSplitter, QApplication
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QPixmap, QIcon, QMovie

from ui.style_manager import StyleManager


class VideoSynthesisDialog(QDialog):
    """视频合成对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("视频合成 - FlipTalk AI")
        self.setFixedSize(1000, 700)
        self.setModal(True)
        
        # 初始化变量
        self.video_file_path = None
        self.audio_segments_dir = None
        self.subtitle_file_path = None
        self.background_music_path = None
        self.output_dir = "output/synthesis"
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题区域
        title_layout = QHBoxLayout()
        title_icon = QLabel("🎬")
        title_icon.setStyleSheet("font-size: 32px;")
        title_text = QLabel("视频合成")
        title_text.setStyleSheet("font-size: 24px; font-weight: bold; color: #2C3E50;")
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：文件选择区域
        left_widget = self.create_file_selection_area()
        splitter.addWidget(left_widget)
        
        # 右侧：设置和预览区域
        right_widget = self.create_settings_area()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
        
        # 底部按钮区域
        button_layout = QHBoxLayout()
        
        self.preview_button = QPushButton("🔍 预览合成")
        self.preview_button.setEnabled(False)
        self.preview_button.clicked.connect(self.preview_synthesis)
        
        self.start_button = QPushButton("🚀 开始合成")
        self.start_button.setEnabled(False)
        self.start_button.clicked.connect(self.start_synthesis)
        
        self.close_button = QPushButton("❌ 关闭")
        self.close_button.clicked.connect(self.close)
        
        button_layout.addStretch()
        button_layout.addWidget(self.preview_button)
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
        # 进度条（初始隐藏）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("请选择需要的文件开始视频合成")
        self.status_label.setStyleSheet("color: #CCCCCC; font-style: italic;")
        layout.addWidget(self.status_label)
    
    def create_file_selection_area(self):
        """创建文件选择区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 文件选择组
        file_group = QGroupBox("📁 文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 原视频文件选择
        video_layout = QHBoxLayout()
        video_layout.addWidget(QLabel("原视频文件:"))
        self.video_file_label = QLabel("未选择")
        self.video_file_label.setStyleSheet("color: #FF6B6B; font-style: italic;")
        video_layout.addWidget(self.video_file_label)
        video_layout.addStretch()
        
        video_button = QPushButton("选择视频")
        video_button.clicked.connect(self.select_video_file)
        video_layout.addWidget(video_button)
        
        file_layout.addLayout(video_layout)
        
        # 音频片段目录选择
        audio_layout = QHBoxLayout()
        audio_layout.addWidget(QLabel("音频片段目录:"))
        self.audio_dir_label = QLabel("未选择")
        self.audio_dir_label.setStyleSheet("color: #FF6B6B; font-style: italic;")
        audio_layout.addWidget(self.audio_dir_label)
        audio_layout.addStretch()
        
        audio_button = QPushButton("选择目录")
        audio_button.clicked.connect(self.select_audio_directory)
        audio_layout.addWidget(audio_button)
        
        file_layout.addLayout(audio_layout)
        
        # 字幕文件选择
        subtitle_layout = QHBoxLayout()
        subtitle_layout.addWidget(QLabel("字幕文件:"))
        self.subtitle_file_label = QLabel("未选择")
        self.subtitle_file_label.setStyleSheet("color: #FF6B6B; font-style: italic;")
        subtitle_layout.addWidget(self.subtitle_file_label)
        subtitle_layout.addStretch()
        
        subtitle_button = QPushButton("选择字幕")
        subtitle_button.clicked.connect(self.select_subtitle_file)
        subtitle_layout.addWidget(subtitle_button)
        
        file_layout.addLayout(subtitle_layout)
        
        layout.addWidget(file_group)
        
        # 音频片段列表
        segments_group = QGroupBox("🎵 音频片段列表")
        segments_layout = QVBoxLayout(segments_group)
        
        self.segments_list = QListWidget()
        self.segments_list.setMaximumHeight(200)
        segments_layout.addWidget(self.segments_list)
        
        refresh_button = QPushButton("🔄 刷新列表")
        refresh_button.clicked.connect(self.refresh_segments_list)
        segments_layout.addWidget(refresh_button)
        
        layout.addWidget(segments_group)
        
        return widget
    
    def create_settings_area(self):
        """创建设置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 合成设置组
        settings_group = QGroupBox("⚙️ 合成设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 背景音乐选项
        bg_music_layout = QHBoxLayout()
        self.keep_background_music = QCheckBox("保留背景音乐")
        self.keep_background_music.setChecked(True)
        self.keep_background_music.stateChanged.connect(self.on_background_music_changed)
        bg_music_layout.addWidget(self.keep_background_music)
        
        self.bg_music_button = QPushButton("选择背景音乐")
        self.bg_music_button.clicked.connect(self.select_background_music)
        bg_music_layout.addWidget(self.bg_music_button)
        bg_music_layout.addStretch()
        
        settings_layout.addLayout(bg_music_layout)
        
        self.bg_music_label = QLabel("背景音乐: 未选择")
        self.bg_music_label.setStyleSheet("color: #CCCCCC; font-size: 12px;")
        settings_layout.addWidget(self.bg_music_label)
        
        # 音量设置
        volume_layout = QGridLayout()
        
        volume_layout.addWidget(QLabel("配音音量:"), 0, 0)
        self.voiceover_volume = QSpinBox()
        self.voiceover_volume.setRange(0, 200)
        self.voiceover_volume.setValue(100)
        self.voiceover_volume.setSuffix("%")
        volume_layout.addWidget(self.voiceover_volume, 0, 1)
        
        volume_layout.addWidget(QLabel("背景音乐音量:"), 1, 0)
        self.background_volume = QSpinBox()
        self.background_volume.setRange(0, 100)
        self.background_volume.setValue(30)
        self.background_volume.setSuffix("%")
        volume_layout.addWidget(self.background_volume, 1, 1)
        
        settings_layout.addLayout(volume_layout)
        
        layout.addWidget(settings_group)
        
        # 输出设置组
        output_group = QGroupBox("📤 输出设置")
        output_layout = QVBoxLayout(output_group)
        
        # 输出目录
        output_dir_layout = QHBoxLayout()
        output_dir_layout.addWidget(QLabel("输出目录:"))
        self.output_dir_label = QLabel(self.output_dir)
        self.output_dir_label.setStyleSheet("color: #4A9EFF; font-weight: bold;")
        output_dir_layout.addWidget(self.output_dir_label)
        output_dir_layout.addStretch()
        
        output_dir_button = QPushButton("更改")
        output_dir_button.clicked.connect(self.select_output_directory)
        output_dir_layout.addWidget(output_dir_button)
        
        output_layout.addLayout(output_dir_layout)
        
        # 输出格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("输出格式:"))
        self.output_format = QComboBox()
        self.output_format.addItems(["MP4 (推荐)", "AVI", "MOV", "MKV"])
        format_layout.addWidget(self.output_format)
        format_layout.addStretch()
        
        output_layout.addLayout(format_layout)
        
        layout.addWidget(output_group)
        
        # 预览区域
        preview_group = QGroupBox("👁️ 合成预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setPlaceholderText("合成预览信息将在这里显示...")
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)

        return widget

    def apply_styles(self):
        """应用样式 - 深色主题"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1E1E1E;
                color: #FFFFFF;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #3A3A3A;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #2A2A2A;
                color: #FFFFFF;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #4A9EFF;
                font-size: 14px;
                font-weight: bold;
            }

            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A9EFF, stop:1 #357ABD);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-height: 16px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5AAEEF, stop:1 #4A9EFF);
                transform: translateY(-1px);
            }

            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #357ABD, stop:1 #2E6BA8);
                transform: translateY(1px);
            }

            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }

            QListWidget {
                border: 1px solid #3A3A3A;
                border-radius: 8px;
                background-color: #2A2A2A;
                color: #FFFFFF;
                padding: 5px;
                selection-background-color: #4A9EFF;
            }

            QListWidget::item {
                padding: 8px;
                border-radius: 4px;
                margin: 2px;
            }

            QListWidget::item:hover {
                background-color: #3A3A3A;
            }

            QListWidget::item:selected {
                background-color: #4A9EFF;
                color: white;
            }

            QTextEdit {
                border: 1px solid #3A3A3A;
                border-radius: 8px;
                background-color: #2A2A2A;
                color: #FFFFFF;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }

            QLabel {
                color: #FFFFFF;
                font-size: 13px;
            }

            QCheckBox {
                font-weight: bold;
                color: #FFFFFF;
                font-size: 13px;
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid #3A3A3A;
                background-color: #2A2A2A;
            }

            QCheckBox::indicator:hover {
                border-color: #4A9EFF;
            }

            QCheckBox::indicator:checked {
                background-color: #4A9EFF;
                border: 2px solid #4A9EFF;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }

            QSpinBox, QDoubleSpinBox {
                border: 1px solid #3A3A3A;
                border-radius: 6px;
                background-color: #2A2A2A;
                color: #FFFFFF;
                padding: 6px 8px;
                font-size: 13px;
                min-height: 20px;
            }

            QSpinBox:hover, QDoubleSpinBox:hover {
                border-color: #4A9EFF;
            }

            QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #4A9EFF;
                outline: none;
            }

            QSpinBox::up-button, QDoubleSpinBox::up-button {
                background-color: #3A3A3A;
                border: none;
                border-radius: 3px;
                width: 16px;
            }

            QSpinBox::down-button, QDoubleSpinBox::down-button {
                background-color: #3A3A3A;
                border: none;
                border-radius: 3px;
                width: 16px;
            }

            QComboBox {
                border: 1px solid #3A3A3A;
                border-radius: 6px;
                background-color: #2A2A2A;
                color: #FFFFFF;
                padding: 6px 8px;
                font-size: 13px;
                min-height: 20px;
            }

            QComboBox:hover {
                border-color: #4A9EFF;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNC41TDYgNy41TDkgNC41IiBzdHJva2U9IiNGRkZGRkYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
                width: 12px;
                height: 12px;
            }

            QComboBox QAbstractItemView {
                border: 1px solid #3A3A3A;
                background-color: #2A2A2A;
                color: #FFFFFF;
                selection-background-color: #4A9EFF;
                border-radius: 6px;
            }

            QProgressBar {
                border: 1px solid #3A3A3A;
                border-radius: 8px;
                background-color: #2A2A2A;
                text-align: center;
                color: #FFFFFF;
                font-weight: bold;
                height: 20px;
            }

            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4A9EFF, stop:1 #357ABD);
                border-radius: 7px;
                margin: 1px;
            }

            QSplitter::handle {
                background-color: #3A3A3A;
                width: 2px;
                height: 2px;
            }

            QSplitter::handle:hover {
                background-color: #4A9EFF;
            }
        """)

    def select_video_file(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
        )

        if file_path:
            self.video_file_path = file_path
            file_name = os.path.basename(file_path)
            self.video_file_label.setText(file_name)
            self.video_file_label.setStyleSheet("color: #4A9EFF; font-weight: bold;")
            self.status_label.setText(f"已选择视频文件: {file_name}")
            self.check_ready_state()

    def select_audio_directory(self):
        """选择音频片段目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择音频片段目录",
            ""
        )

        if dir_path:
            self.audio_segments_dir = dir_path
            dir_name = os.path.basename(dir_path)
            self.audio_dir_label.setText(dir_name)
            self.audio_dir_label.setStyleSheet("color: #4A9EFF; font-weight: bold;")
            self.status_label.setText(f"已选择音频目录: {dir_name}")
            self.refresh_segments_list()
            self.check_ready_state()

    def select_subtitle_file(self):
        """选择字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择字幕文件",
            "",
            "字幕文件 (*.srt *.vtt *.ass *.ssa);;所有文件 (*)"
        )

        if file_path:
            self.subtitle_file_path = file_path
            file_name = os.path.basename(file_path)
            self.subtitle_file_label.setText(file_name)
            self.subtitle_file_label.setStyleSheet("color: #4A9EFF; font-weight: bold;")
            self.status_label.setText(f"已选择字幕文件: {file_name}")
            self.check_ready_state()

    def select_background_music(self):
        """选择背景音乐文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择背景音乐文件",
            "",
            "音频文件 (*.wav *.mp3 *.aac *.flac *.ogg);;所有文件 (*)"
        )

        if file_path:
            self.background_music_path = file_path
            file_name = os.path.basename(file_path)
            self.bg_music_label.setText(f"背景音乐: {file_name}")
            self.bg_music_label.setStyleSheet("color: #4A9EFF; font-size: 12px; font-weight: bold;")

    def select_output_directory(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            self.output_dir
        )

        if dir_path:
            self.output_dir = dir_path
            self.output_dir_label.setText(dir_path)
            self.output_dir_label.setStyleSheet("color: #4A9EFF; font-weight: bold;")

    def on_background_music_changed(self, state):
        """背景音乐选项变化处理"""
        enabled = state == Qt.Checked
        self.bg_music_button.setEnabled(enabled)
        self.background_volume.setEnabled(enabled)

        if not enabled:
            self.bg_music_label.setText("背景音乐: 已禁用")
            self.bg_music_label.setStyleSheet("color: #808080; font-size: 12px;")

    def refresh_segments_list(self):
        """刷新音频片段列表"""
        self.segments_list.clear()

        if not self.audio_segments_dir or not os.path.exists(self.audio_segments_dir):
            return

        # 查找音频文件
        audio_extensions = ['.wav', '.mp3', '.aac', '.flac', '.ogg']
        audio_files = []

        for file in os.listdir(self.audio_segments_dir):
            if any(file.lower().endswith(ext) for ext in audio_extensions):
                audio_files.append(file)

        # 按文件名排序
        audio_files.sort()

        for audio_file in audio_files:
            item = QListWidgetItem(f"🎵 {audio_file}")
            self.segments_list.addItem(item)

        if audio_files:
            self.status_label.setText(f"找到 {len(audio_files)} 个音频片段")
        else:
            self.status_label.setText("音频目录中未找到音频文件")

    def check_ready_state(self):
        """检查是否准备就绪"""
        ready = (
            self.video_file_path and
            self.audio_segments_dir and
            self.subtitle_file_path and
            os.path.exists(self.video_file_path) and
            os.path.exists(self.audio_segments_dir) and
            os.path.exists(self.subtitle_file_path)
        )

        self.preview_button.setEnabled(ready)
        self.start_button.setEnabled(ready)

        if ready:
            self.status_label.setText("✅ 准备就绪，可以开始合成")
            self.status_label.setStyleSheet("color: #4A9EFF; font-weight: bold;")

    def preview_synthesis(self):
        """预览合成"""
        try:
            preview_info = []
            preview_info.append("=== 视频合成预览 ===\n")

            # 视频信息
            preview_info.append(f"📹 原视频: {os.path.basename(self.video_file_path)}")

            # 音频片段信息
            if self.audio_segments_dir:
                audio_files = [f for f in os.listdir(self.audio_segments_dir)
                             if f.lower().endswith(('.wav', '.mp3', '.aac', '.flac', '.ogg'))]
                preview_info.append(f"🎵 音频片段: {len(audio_files)} 个文件")
                for i, audio_file in enumerate(sorted(audio_files)[:5]):  # 只显示前5个
                    preview_info.append(f"  {i+1}. {audio_file}")
                if len(audio_files) > 5:
                    preview_info.append(f"  ... 还有 {len(audio_files) - 5} 个文件")

            # 字幕信息
            preview_info.append(f"📝 字幕文件: {os.path.basename(self.subtitle_file_path)}")

            # 背景音乐信息
            if self.keep_background_music.isChecked():
                if self.background_music_path:
                    preview_info.append(f"🎼 背景音乐: {os.path.basename(self.background_music_path)}")
                    preview_info.append(f"   音量: {self.background_volume.value()}%")
                else:
                    preview_info.append("🎼 背景音乐: 将从原视频中提取")
            else:
                preview_info.append("🎼 背景音乐: 已禁用")

            # 输出设置
            preview_info.append(f"\n📤 输出目录: {self.output_dir}")
            preview_info.append(f"📤 输出格式: {self.output_format.currentText()}")
            preview_info.append(f"🔊 配音音量: {self.voiceover_volume.value()}%")

            self.preview_text.setText("\n".join(preview_info))

        except Exception as e:
            QMessageBox.warning(self, "预览错误", f"生成预览时出错: {str(e)}")

    def start_synthesis(self):
        """开始合成"""
        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # 禁用按钮
            self.start_button.setEnabled(False)
            self.preview_button.setEnabled(False)

            # 创建合成线程
            self.synthesis_thread = VideoSynthesisThread(
                video_path=self.video_file_path,
                audio_dir=self.audio_segments_dir,
                subtitle_path=self.subtitle_file_path,
                background_music_path=self.background_music_path if self.keep_background_music.isChecked() else None,
                output_dir=self.output_dir,
                voiceover_volume=self.voiceover_volume.value(),
                background_volume=self.background_volume.value(),
                output_format=self.output_format.currentText().split()[0].lower()
            )

            # 连接信号
            self.synthesis_thread.progress_updated.connect(self.progress_bar.setValue)
            self.synthesis_thread.status_updated.connect(self.status_label.setText)
            self.synthesis_thread.synthesis_completed.connect(self.on_synthesis_completed)
            self.synthesis_thread.synthesis_failed.connect(self.on_synthesis_failed)

            # 启动线程
            self.synthesis_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "合成错误", f"启动合成时出错: {str(e)}")
            self.reset_ui_state()

    def on_synthesis_completed(self, output_path):
        """合成完成处理"""
        self.progress_bar.setValue(100)
        self.status_label.setText(f"✅ 合成完成: {os.path.basename(output_path)}")

        # 显示完成对话框
        reply = QMessageBox.question(
            self,
            "合成完成",
            f"视频合成已完成！\n\n输出文件: {output_path}\n\n是否打开输出目录？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 打开输出目录
            import subprocess
            import platform

            if platform.system() == "Windows":
                subprocess.run(["explorer", os.path.dirname(output_path)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", os.path.dirname(output_path)])
            else:  # Linux
                subprocess.run(["xdg-open", os.path.dirname(output_path)])

        self.reset_ui_state()

    def on_synthesis_failed(self, error_message):
        """合成失败处理"""
        self.status_label.setText(f"❌ 合成失败: {error_message}")
        QMessageBox.critical(self, "合成失败", f"视频合成失败:\n\n{error_message}")
        self.reset_ui_state()

    def reset_ui_state(self):
        """重置UI状态"""
        self.progress_bar.setVisible(False)
        self.start_button.setEnabled(True)
        self.preview_button.setEnabled(True)
        self.check_ready_state()


class VideoSynthesisThread(QThread):
    """视频合成线程"""

    progress_updated = Signal(int)
    status_updated = Signal(str)
    synthesis_completed = Signal(str)
    synthesis_failed = Signal(str)

    def __init__(self, video_path, audio_dir, subtitle_path, background_music_path,
                 output_dir, voiceover_volume, background_volume, output_format):
        super().__init__()
        self.video_path = video_path
        self.audio_dir = audio_dir
        self.subtitle_path = subtitle_path
        self.background_music_path = background_music_path
        self.output_dir = output_dir
        self.voiceover_volume = voiceover_volume
        self.background_volume = background_volume
        self.output_format = output_format

    def run(self):
        """执行视频合成"""
        try:
            self.status_updated.emit("🔍 分析字幕文件...")
            self.progress_updated.emit(10)

            # 1. 解析字幕文件
            subtitle_segments = self.parse_subtitle_file()
            if not subtitle_segments:
                self.synthesis_failed.emit("字幕文件解析失败或为空")
                return

            self.status_updated.emit("🎵 合并音频片段...")
            self.progress_updated.emit(30)

            # 2. 合并音频片段
            combined_audio_path = self.combine_audio_segments(subtitle_segments)
            if not combined_audio_path:
                self.synthesis_failed.emit("音频片段合并失败")
                return

            # 3. 处理背景音乐（如果需要）
            final_audio_path = combined_audio_path
            if self.background_music_path:
                self.status_updated.emit("🎼 混合背景音乐...")
                self.progress_updated.emit(60)

                final_audio_path = self.mix_background_music(combined_audio_path)
                if not final_audio_path:
                    self.status_updated.emit("⚠️ 背景音乐混合失败，使用纯配音")
                    final_audio_path = combined_audio_path

            self.status_updated.emit("🎬 替换视频音轨...")
            self.progress_updated.emit(80)

            # 4. 替换视频音轨
            output_path = self.replace_video_audio(final_audio_path)
            if not output_path:
                self.synthesis_failed.emit("视频音轨替换失败")
                return

            self.progress_updated.emit(100)
            self.synthesis_completed.emit(output_path)

        except Exception as e:
            self.synthesis_failed.emit(str(e))

    def parse_subtitle_file(self):
        """解析字幕文件"""
        try:
            import re

            segments = []

            with open(self.subtitle_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if self.subtitle_path.lower().endswith('.srt'):
                # 解析SRT格式
                pattern = r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)'
                matches = re.findall(pattern, content, re.DOTALL)

                for match in matches:
                    index = int(match[0])
                    start_time = self.parse_time(match[1])
                    end_time = self.parse_time(match[2])
                    text = match[3].strip()

                    segments.append({
                        'index': index,
                        'start': start_time,
                        'end': end_time,
                        'text': text
                    })

            return sorted(segments, key=lambda x: x['start'])

        except Exception as e:
            print(f"解析字幕文件失败: {e}")
            return []

    def parse_time(self, time_str):
        """解析时间字符串为秒数"""
        try:
            # SRT格式: 00:01:23,456
            time_str = time_str.replace(',', '.')
            parts = time_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])

            return hours * 3600 + minutes * 60 + seconds

        except Exception as e:
            print(f"解析时间失败: {e}")
            return 0.0

    def combine_audio_segments(self, subtitle_segments):
        """合并音频片段"""
        try:
            from pydub import AudioSegment
            import glob

            # 获取音频文件列表
            audio_files = []
            for ext in ['*.wav', '*.mp3', '*.aac', '*.flac', '*.ogg']:
                audio_files.extend(glob.glob(os.path.join(self.audio_dir, ext)))

            if not audio_files:
                raise Exception("音频目录中未找到音频文件")

            # 按文件名排序
            audio_files.sort()

            # 创建空的音频轨道
            combined_audio = AudioSegment.silent(duration=0)

            # 按字幕时间戳合并音频
            for i, segment in enumerate(subtitle_segments):
                if i < len(audio_files):
                    # 加载对应的音频文件
                    audio_segment = AudioSegment.from_file(audio_files[i])

                    # 调整音量
                    if self.voiceover_volume != 100:
                        volume_change = 20 * (self.voiceover_volume / 100.0 - 1)
                        audio_segment = audio_segment + volume_change

                    # 计算需要的静音时长
                    if i == 0:
                        # 第一个片段，添加开始时间的静音
                        silence_duration = segment['start'] * 1000  # 转换为毫秒
                        if silence_duration > 0:
                            combined_audio += AudioSegment.silent(duration=silence_duration)
                    else:
                        # 后续片段，计算与前一个片段的间隔
                        prev_segment = subtitle_segments[i-1]
                        gap_duration = (segment['start'] - prev_segment['end']) * 1000
                        if gap_duration > 0:
                            combined_audio += AudioSegment.silent(duration=gap_duration)

                    # 添加音频片段
                    combined_audio += audio_segment

            # 导出合并后的音频
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"combined_voiceover_{timestamp}.wav"
            output_path = os.path.join(self.output_dir, output_filename)

            combined_audio.export(output_path, format="wav")

            return output_path

        except Exception as e:
            print(f"合并音频片段失败: {e}")
            return None

    def mix_background_music(self, voiceover_path):
        """混合背景音乐"""
        try:
            from pydub import AudioSegment

            # 加载配音音频
            voiceover_audio = AudioSegment.from_wav(voiceover_path)

            # 加载背景音乐
            if self.background_music_path:
                # 使用指定的背景音乐文件
                background_music = AudioSegment.from_file(self.background_music_path)
            else:
                # 从原视频提取背景音乐（需要人声分离）
                background_music = self.extract_background_from_video()
                if not background_music:
                    return None

            # 调整背景音乐长度以匹配配音长度
            voiceover_duration = len(voiceover_audio)
            background_duration = len(background_music)

            if background_duration > voiceover_duration:
                # 背景音乐较长，截取前面部分
                background_music = background_music[:voiceover_duration]
            elif background_duration < voiceover_duration:
                # 背景音乐较短，循环播放
                repeat_times = int(voiceover_duration / background_duration) + 1
                background_music = background_music * repeat_times
                background_music = background_music[:voiceover_duration]

            # 调整背景音乐音量
            if self.background_volume != 100:
                volume_change = 20 * (self.background_volume / 100.0 - 1)
                background_music = background_music + volume_change

            # 混合音频
            mixed_audio = voiceover_audio.overlay(background_music)

            # 导出混合后的音频
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"mixed_audio_with_background_{timestamp}.wav"
            output_path = os.path.join(self.output_dir, output_filename)

            mixed_audio.export(output_path, format="wav")

            return output_path

        except Exception as e:
            print(f"混合背景音乐失败: {e}")
            return None

    def extract_background_from_video(self):
        """从原视频提取背景音乐（需要人声分离）"""
        try:
            # 这里应该调用人声分离功能
            # 暂时返回None，表示需要用户手动指定背景音乐
            return None

        except Exception as e:
            print(f"从视频提取背景音乐失败: {e}")
            return None

    def replace_video_audio(self, audio_path):
        """替换视频音轨"""
        try:
            import subprocess

            # 生成输出文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            video_name = os.path.splitext(os.path.basename(self.video_path))[0]
            output_filename = f"{video_name}_synthesized_{timestamp}.{self.output_format}"
            output_path = os.path.join(self.output_dir, output_filename)

            # FFmpeg命令
            cmd = [
                "ffmpeg",
                "-i", self.video_path,
                "-i", audio_path,
                "-c:v", "copy",
                "-c:a", "aac",
                "-b:a", "192k",
                "-ar", "44100",
                "-ac", "2",
                "-map", "0:v:0",
                "-map", "1:a:0",
                "-shortest",
                "-avoid_negative_ts", "make_zero",
                "-y",
                output_path
            ]

            # 执行FFmpeg命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                return output_path
            else:
                raise Exception(f"FFmpeg错误: {result.stderr}")

        except Exception as e:
            print(f"替换视频音轨失败: {e}")
            return None
