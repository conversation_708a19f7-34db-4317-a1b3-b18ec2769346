#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI 日志显示面板
提供日志查看、过滤、搜索和导出功能
"""

import os
import datetime
from typing import List, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QCheckBox, QLabel, QLineEdit, QFileDialog, QMessageBox,
    QGroupBox, QGridLayout, QFrame, QScrollArea, QComboBox,
    QSpinBox, QProgressBar, QSplitter
)
from PySide6.QtCore import Qt, QTimer, Signal, Slot
from PySide6.QtGui import QFont, QTextCursor, QTextCharFormat, QColor

from core.logger import LogManager, LogLevel, LogEntry, get_logger


class LogDisplayWidget(QTextEdit):
    """日志显示组件 - 支持颜色和搜索"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置为只读
        self.setReadOnly(True)
        
        # 设置字体
        font = QFont("Consolas", 10)
        font.setStyleHint(QFont.Monospace)
        self.setFont(font)
        
        # 获取日志管理器
        self._logger = get_logger()
        self._color_map = self._logger.get_color_map()
        
        # 搜索相关
        self._search_text = ""
        self._search_results = []
        self._current_search_index = -1
        
        # 设置样式
        self.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #3A3A3A;
                border-radius: 8px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #4A9EFF;
                outline: none;
            }
        """)
    
    def add_log_entry(self, entry: LogEntry):
        """添加日志条目"""
        # 移动到文档末尾
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        # 设置颜色格式
        format = QTextCharFormat()
        color = self._color_map.get(entry.level, "#FFFFFF")
        format.setForeground(QColor(color))
        
        # 插入日志文本
        cursor.insertText(str(entry) + "\n", format)
        
        # 自动滚动到底部
        self.ensureCursorVisible()
    
    def clear_logs(self):
        """清除所有日志"""
        self.clear()
        self._search_results.clear()
        self._current_search_index = -1
    
    def search_text(self, text: str) -> int:
        """搜索文本，返回匹配数量"""
        self._search_text = text
        self._search_results.clear()
        self._current_search_index = -1
        
        if not text:
            return 0
        
        # 清除之前的高亮
        cursor = self.textCursor()
        cursor.select(QTextCursor.Document)
        format = QTextCharFormat()
        cursor.mergeCharFormat(format)
        
        # 搜索并高亮
        document = self.document()
        cursor = QTextCursor(document)
        
        highlight_format = QTextCharFormat()
        highlight_format.setBackground(QColor("#FFA726"))
        highlight_format.setForeground(QColor("#000000"))
        
        while True:
            cursor = document.find(text, cursor)
            if cursor.isNull():
                break
            
            self._search_results.append(cursor.position())
            cursor.mergeCharFormat(highlight_format)
        
        return len(self._search_results)
    
    def go_to_next_search_result(self):
        """跳转到下一个搜索结果"""
        if not self._search_results:
            return
        
        self._current_search_index = (self._current_search_index + 1) % len(self._search_results)
        position = self._search_results[self._current_search_index]
        
        cursor = self.textCursor()
        cursor.setPosition(position)
        cursor.movePosition(QTextCursor.Right, QTextCursor.KeepAnchor, len(self._search_text))
        self.setTextCursor(cursor)
        self.ensureCursorVisible()
    
    def go_to_previous_search_result(self):
        """跳转到上一个搜索结果"""
        if not self._search_results:
            return
        
        self._current_search_index = (self._current_search_index - 1) % len(self._search_results)
        position = self._search_results[self._current_search_index]
        
        cursor = self.textCursor()
        cursor.setPosition(position)
        cursor.movePosition(QTextCursor.Right, QTextCursor.KeepAnchor, len(self._search_text))
        self.setTextCursor(cursor)
        self.ensureCursorVisible()


class LogFilterWidget(QWidget):
    """日志过滤控制组件"""
    
    filter_changed = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._logger = get_logger()
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI - 现代化过滤面板"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # 标题
        title_container = QFrame()
        title_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4A9EFF, stop:1 #357ABD);
                border-radius: 10px;
                border: none;
                margin-bottom: 5px;
            }
        """)

        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(15, 10, 15, 10)

        title_icon = QLabel("🎛️")
        title_icon.setStyleSheet("font-size: 18px; background: transparent;")

        title_text = QLabel("日志控制")
        title_text.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)

        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_text)
        title_layout.addStretch()

        layout.addWidget(title_container)

        # 级别过滤卡片
        level_card = self.create_level_filter_card()
        layout.addWidget(level_card)

        # 设置卡片
        settings_card = self.create_settings_card()
        layout.addWidget(settings_card)

        # 统计信息卡片
        stats_card = self.create_stats_card()
        layout.addWidget(stats_card)

        layout.addStretch()

    def create_level_filter_card(self):
        """创建级别过滤卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: #3A3A3A;
                border-radius: 10px;
                border: 1px solid #555555;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # 卡片标题
        header = QLabel("📊 日志级别")
        header.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 5px;
        """)
        layout.addWidget(header)

        # 级别复选框
        self.level_checkboxes = {}
        level_colors = {
            LogLevel.ERROR: "#FF6B6B",
            LogLevel.WARNING: "#FFA726",
            LogLevel.INFO: "#4A9EFF",
            LogLevel.DEBUG: "#CCCCCC"
        }

        for level in LogLevel:
            checkbox_container = QFrame()
            checkbox_container.setStyleSheet("""
                QFrame {
                    background-color: #2A2A2A;
                    border-radius: 6px;
                    border: 1px solid #555555;
                }
                QFrame:hover {
                    background-color: #404040;
                    border-color: #4A9EFF;
                }
            """)

            checkbox_layout = QHBoxLayout(checkbox_container)
            checkbox_layout.setContentsMargins(10, 8, 10, 8)

            # 颜色指示器
            color_indicator = QLabel("●")
            color_indicator.setStyleSheet(f"""
                color: {level_colors[level]};
                font-size: 14px;
                font-weight: bold;
            """)

            checkbox = QCheckBox(level.name)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.on_filter_changed)
            checkbox.setStyleSheet("""
                QCheckBox {
                    color: #FFFFFF;
                    font-size: 12px;
                    font-weight: 500;
                    spacing: 8px;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                    border-radius: 3px;
                    border: 2px solid #3A3A3A;
                    background-color: transparent;
                }
                QCheckBox::indicator:checked {
                    background-color: #4A9EFF;
                    border-color: #4A9EFF;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
                }
                QCheckBox::indicator:hover {
                    border-color: #4A9EFF;
                }
            """)

            self.level_checkboxes[level] = checkbox

            checkbox_layout.addWidget(color_indicator)
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.addStretch()

            layout.addWidget(checkbox_container)

        return card

    def create_settings_card(self):
        """创建设置卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: #3A3A3A;
                border-radius: 10px;
                border: 1px solid #555555;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # 卡片标题
        header = QLabel("⚙️ 设置")
        header.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 5px;
        """)
        layout.addWidget(header)

        # 最大日志数设置
        max_logs_container = QFrame()
        max_logs_container.setStyleSheet("""
            QFrame {
                background-color: #2A2A2A;
                border-radius: 6px;
                border: 1px solid #555555;
            }
        """)

        max_logs_layout = QHBoxLayout(max_logs_container)
        max_logs_layout.setContentsMargins(10, 8, 10, 8)

        max_logs_label = QLabel("最大条数:")
        max_logs_label.setStyleSheet("""
            color: #FFFFFF;
            font-size: 12px;
            font-weight: 500;
        """)

        self.max_logs_spinbox = QSpinBox()
        self.max_logs_spinbox.setRange(100, 10000)
        self.max_logs_spinbox.setValue(1000)
        self.max_logs_spinbox.setSuffix(" 条")
        self.max_logs_spinbox.valueChanged.connect(self.on_max_logs_changed)
        self.max_logs_spinbox.setStyleSheet("""
            QSpinBox {
                border: 1px solid #3A3A3A;
                border-radius: 4px;
                background-color: #2A2A2A;
                color: #FFFFFF;
                padding: 4px 8px;
                font-size: 11px;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #4A9EFF;
                outline: none;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #3A3A3A;
                border: none;
                border-radius: 2px;
                width: 16px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #4A9EFF;
            }
        """)

        max_logs_layout.addWidget(max_logs_label)
        max_logs_layout.addStretch()
        max_logs_layout.addWidget(self.max_logs_spinbox)

        layout.addWidget(max_logs_container)

        return card

    def create_stats_card(self):
        """创建统计信息卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: #3A3A3A;
                border-radius: 10px;
                border: 1px solid #555555;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # 卡片标题
        header = QLabel("📈 统计信息")
        header.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 5px;
        """)
        layout.addWidget(header)

        # 统计信息显示
        self.stats_label = QLabel("统计信息加载中...")
        self.stats_label.setStyleSheet("""
            color: #FFFFFF;
            font-size: 12px;
            font-family: 'JetBrains Mono', 'Consolas', monospace;
            line-height: 1.6;
            background-color: #2A2A2A;
            border-radius: 6px;
            padding: 12px;
            border: 1px solid #555555;
        """)
        layout.addWidget(self.stats_label)

        return card
        

    
    @Slot()
    def on_filter_changed(self):
        """过滤条件改变"""
        # 更新日志管理器的过滤设置
        for level, checkbox in self.level_checkboxes.items():
            self._logger.set_level_enabled(level, checkbox.isChecked())
        
        self.filter_changed.emit()
    
    @Slot(int)
    def on_max_logs_changed(self, value):
        """最大日志数改变"""
        self._logger.set_max_logs(value)
    
    def update_stats(self):
        """更新统计信息"""
        stats = self._logger.get_stats()

        # 创建带颜色指示器的统计信息
        stats_html = f"""
        <div style="line-height: 1.6;">
            <div style="margin-bottom: 8px;">
                <span style="color: #FFFFFF; font-weight: bold;">总计:</span>
                <span style="color: #4A9EFF; font-weight: bold;">{stats['TOTAL']} 条</span>
            </div>
            <div style="margin-bottom: 4px;">
                <span style="color: #FF6B6B;">●</span>
                <span style="color: #FFFFFF;">错误:</span>
                <span style="color: #FF6B6B; font-weight: bold;">{stats['ERROR']}</span>
            </div>
            <div style="margin-bottom: 4px;">
                <span style="color: #FFA726;">●</span>
                <span style="color: #FFFFFF;">警告:</span>
                <span style="color: #FFA726; font-weight: bold;">{stats['WARNING']}</span>
            </div>
            <div style="margin-bottom: 4px;">
                <span style="color: #4A9EFF;">●</span>
                <span style="color: #FFFFFF;">信息:</span>
                <span style="color: #4A9EFF; font-weight: bold;">{stats['INFO']}</span>
            </div>
            <div>
                <span style="color: #CCCCCC;">●</span>
                <span style="color: #FFFFFF;">调试:</span>
                <span style="color: #CCCCCC; font-weight: bold;">{stats['DEBUG']}</span>
            </div>
        </div>
        """

        self.stats_label.setText(stats_html)
    
    def get_enabled_levels(self) -> List[LogLevel]:
        """获取启用的日志级别"""
        return [level for level, checkbox in self.level_checkboxes.items() 
                if checkbox.isChecked()]


class LogToolbarWidget(QWidget):
    """日志工具栏组件"""
    
    clear_requested = Signal()
    export_requested = Signal()
    search_requested = Signal(str)
    search_next = Signal()
    search_previous = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI - 现代化工具栏"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 左侧按钮组
        left_group = QFrame()
        left_group.setStyleSheet("""
            QFrame {
                background-color: rgba(74, 158, 255, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(74, 158, 255, 0.3);
            }
        """)

        left_layout = QHBoxLayout(left_group)
        left_layout.setContentsMargins(10, 5, 10, 5)
        left_layout.setSpacing(8)

        # 清除按钮
        self.clear_button = self.create_action_button("🗑️", "清除日志", "#FF6B6B")
        self.clear_button.clicked.connect(self.clear_requested.emit)
        left_layout.addWidget(self.clear_button)

        # 导出按钮
        self.export_button = self.create_action_button("📤", "导出日志", "#27AE60")
        self.export_button.clicked.connect(self.export_requested.emit)
        left_layout.addWidget(self.export_button)

        layout.addWidget(left_group)

        # 搜索区域
        search_group = QFrame()
        search_group.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                border: 1px solid #3A3A3A;
            }
        """)

        search_layout = QHBoxLayout(search_group)
        search_layout.setContentsMargins(15, 5, 15, 5)
        search_layout.setSpacing(10)

        # 搜索图标
        search_icon = QLabel("🔍")
        search_icon.setStyleSheet("""
            font-size: 16px;
            color: #4A9EFF;
            background: transparent;
        """)
        search_layout.addWidget(search_icon)

        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索日志内容...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background: transparent;
                border: none;
                color: #FFFFFF;
                font-size: 13px;
                padding: 5px 0;
                min-width: 200px;
            }
            QLineEdit::placeholder {
                color: #888888;
            }
            QLineEdit:focus {
                outline: none;
            }
        """)
        self.search_input.textChanged.connect(self.search_requested.emit)
        search_layout.addWidget(self.search_input)

        # 搜索导航按钮
        nav_container = QFrame()
        nav_container.setStyleSheet("background: transparent; border: none;")
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(5)

        self.prev_button = self.create_nav_button("⬆", "上一个")
        self.prev_button.clicked.connect(self.search_previous.emit)
        nav_layout.addWidget(self.prev_button)

        self.next_button = self.create_nav_button("⬇", "下一个")
        self.next_button.clicked.connect(self.search_next.emit)
        nav_layout.addWidget(self.next_button)

        search_layout.addWidget(nav_container)

        # 搜索结果
        self.search_result_label = QLabel("")
        self.search_result_label.setStyleSheet("""
            color: #CCCCCC;
            font-size: 11px;
            font-family: 'JetBrains Mono', 'Consolas', monospace;
            background: transparent;
            min-width: 50px;
        """)
        search_layout.addWidget(self.search_result_label)

        layout.addWidget(search_group)
        layout.addStretch()

    def create_action_button(self, icon: str, tooltip: str, color: str):
        """创建操作按钮"""
        button = QPushButton(icon)
        button.setToolTip(tooltip)
        button.setFixedSize(36, 36)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                border: none;
                border-radius: 18px;
                font-size: 16px;
                color: white;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.lighten_color(color)};
                border: 2px solid rgba(255, 255, 255, 0.3);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color)};
                border: 2px solid rgba(0, 0, 0, 0.3);
            }}
        """)
        return button

    def create_nav_button(self, icon: str, tooltip: str):
        """创建导航按钮"""
        button = QPushButton(icon)
        button.setToolTip(tooltip)
        button.setFixedSize(28, 28)
        button.setStyleSheet("""
            QPushButton {
                background-color: #4A9EFF;
                border: none;
                border-radius: 14px;
                font-size: 12px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5AAEEF;
            }
            QPushButton:pressed {
                background-color: #357ABD;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }
        """)
        return button

    def lighten_color(self, color: str) -> str:
        """使颜色变亮"""
        color_map = {
            "#FF6B6B": "#FF8A8A",
            "#27AE60": "#2ECC71",
            "#4A9EFF": "#5AAEEF"
        }
        return color_map.get(color, color)

    def darken_color(self, color: str) -> str:
        """使颜色变暗"""
        color_map = {
            "#FF6B6B": "#E74C3C",
            "#27AE60": "#229954",
            "#4A9EFF": "#357ABD"
        }
        return color_map.get(color, color)

    
    def update_search_result(self, count: int, current: int):
        """更新搜索结果显示"""
        if count > 0:
            self.search_result_label.setText(f"{current + 1}/{count}")
        else:
            self.search_result_label.setText("0/0" if self.search_input.text() else "")
        
        # 启用/禁用导航按钮
        self.prev_button.setEnabled(count > 0)
        self.next_button.setEnabled(count > 0)


class LogArea(QWidget):
    """日志区域主组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(1359, 776)  # 与其他区域保持一致

        # 获取日志管理器
        self._logger = get_logger()

        # 搜索状态
        self._search_count = 0
        self._search_current = -1

        # 自动刷新定时器
        self._refresh_timer = QTimer()
        self._refresh_timer.timeout.connect(self.refresh_stats)
        self._refresh_timer.start(5000)  # 每5秒刷新一次统计

        self.setup_ui()
        self.connect_signals()
        self.load_existing_logs()

    def setup_ui(self):
        """设置UI - 现代化设计"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 顶部标题卡片
        title_card = self.create_title_card()
        main_layout.addWidget(title_card)

        # 工具栏卡片
        toolbar_card = self.create_toolbar_card()
        main_layout.addWidget(toolbar_card)

        # 主内容区域
        content_card = self.create_content_card()
        main_layout.addWidget(content_card)

        # 底部状态栏
        status_card = self.create_status_card()
        main_layout.addWidget(status_card)

    def create_title_card(self):
        """创建标题卡片"""
        card = QFrame()
        card.setFixedHeight(80)
        card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4A9EFF, stop:1 #357ABD);
                border-radius: 12px;
                border: none;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(25, 15, 25, 15)
        layout.setSpacing(15)

        # 图标
        icon_label = QLabel("📊")
        icon_label.setStyleSheet("""
            font-size: 32px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 8px;
            min-width: 40px;
            max-width: 40px;
            min-height: 40px;
            max-height: 40px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)

        # 标题和描述
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)

        title_label = QLabel("运行日志")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)

        desc_label = QLabel("实时监控系统运行状态和调试信息")
        desc_label.setStyleSheet("""
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            background: transparent;
        """)

        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        text_layout.addStretch()

        layout.addWidget(icon_label)
        layout.addLayout(text_layout)
        layout.addStretch()

        return card

    def create_toolbar_card(self):
        """创建工具栏卡片"""
        card = QFrame()
        card.setFixedHeight(70)
        card.setStyleSheet("""
            QFrame {
                background-color: #2A2A2A;
                border-radius: 10px;
                border: 1px solid #3A3A3A;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(20, 10, 20, 10)

        self.toolbar = LogToolbarWidget()
        self.toolbar.setStyleSheet("background: transparent; border: none;")
        layout.addWidget(self.toolbar)

        return card

    def create_content_card(self):
        """创建内容卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: #2A2A2A;
                border-radius: 10px;
                border: 1px solid #3A3A3A;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 主日志显示区域
        log_container = QFrame()
        log_container.setStyleSheet("""
            QFrame {
                background-color: #1E1E1E;
                border-radius: 8px;
                border: 1px solid #3A3A3A;
            }
        """)

        log_layout = QVBoxLayout(log_container)
        log_layout.setContentsMargins(0, 0, 0, 0)

        self.log_display = LogDisplayWidget()
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                line-height: 1.5;
                color: #FFFFFF;
            }
            QTextEdit:focus {
                outline: none;
            }
            QScrollBar:vertical {
                background-color: #2A2A2A;
                width: 8px;
                border-radius: 4px;
                margin: 0;
            }
            QScrollBar::handle:vertical {
                background-color: #4A9EFF;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #5AAEEF;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        log_layout.addWidget(self.log_display)

        # 右侧过滤面板
        filter_container = QFrame()
        filter_container.setFixedWidth(280)
        filter_container.setStyleSheet("""
            QFrame {
                background-color: #2D2D2D;
                border-radius: 8px;
                border: 2px solid #4A9EFF;
                box-shadow: 0 4px 12px rgba(74, 158, 255, 0.2);
            }
        """)

        filter_layout = QVBoxLayout(filter_container)
        filter_layout.setContentsMargins(0, 0, 0, 0)

        self.filter_widget = LogFilterWidget()
        self.filter_widget.setStyleSheet("background: transparent; border: none;")
        filter_layout.addWidget(self.filter_widget)

        layout.addWidget(log_container, 3)
        layout.addWidget(filter_container, 1)

        return card

    def create_status_card(self):
        """创建状态卡片"""
        card = QFrame()
        card.setFixedHeight(50)
        card.setStyleSheet("""
            QFrame {
                background-color: #2A2A2A;
                border-radius: 10px;
                border: 1px solid #3A3A3A;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(20, 10, 20, 10)

        # 状态指示器
        status_indicator = QLabel("●")
        status_indicator.setStyleSheet("""
            color: #27AE60;
            font-size: 16px;
            font-weight: bold;
        """)

        self.status_label = QLabel("日志系统已就绪")
        self.status_label.setStyleSheet("""
            color: #FFFFFF;
            font-size: 13px;
            font-weight: 500;
        """)

        # 实时时间显示
        self.time_label = QLabel()
        self.time_label.setStyleSheet("""
            color: #CCCCCC;
            font-size: 12px;
            font-family: 'JetBrains Mono', 'Consolas', monospace;
        """)

        # 更新时间显示
        self.update_time_display()

        layout.addWidget(status_indicator)
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.time_label)

        return card

    def update_time_display(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"🕐 {current_time}")

        # 每秒更新一次
        QTimer.singleShot(1000, self.update_time_display)

        # 设置整体样式
        self.setStyleSheet("""
            LogArea {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E1E1E, stop:1 #252525);
                color: #FFFFFF;
            }
        """)

    def connect_signals(self):
        """连接信号"""
        # 日志管理器信号
        self._logger.log_added.connect(self.on_log_added)
        self._logger.logs_cleared.connect(self.on_logs_cleared)

        # 工具栏信号
        self.toolbar.clear_requested.connect(self.clear_logs)
        self.toolbar.export_requested.connect(self.export_logs)
        self.toolbar.search_requested.connect(self.search_logs)
        self.toolbar.search_next.connect(self.search_next)
        self.toolbar.search_previous.connect(self.search_previous)

        # 过滤器信号
        self.filter_widget.filter_changed.connect(self.refresh_display)

    def load_existing_logs(self):
        """加载现有日志"""
        logs = self._logger.get_logs()
        for log in logs:
            self.log_display.add_log_entry(log)

        self.refresh_stats()
        self.status_label.setText(f"已加载 {len(logs)} 条历史日志")

    @Slot(LogEntry)
    def on_log_added(self, entry: LogEntry):
        """处理新日志添加"""
        # 检查是否应该显示此级别的日志
        enabled_levels = self.filter_widget.get_enabled_levels()
        if entry.level in enabled_levels:
            self.log_display.add_log_entry(entry)

        # 更新状态
        self.status_label.setText(f"新日志: [{entry.level.name}] {entry.module}")

    @Slot()
    def on_logs_cleared(self):
        """处理日志清除"""
        self.log_display.clear_logs()
        self.refresh_stats()
        self.status_label.setText("日志已清除")

    @Slot()
    def clear_logs(self):
        """清除日志"""
        reply = QMessageBox.question(
            self,
            "确认清除",
            "确定要清除所有日志吗？此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self._logger.clear_logs()

    @Slot()
    def export_logs(self):
        """导出日志"""
        # 获取当前启用的级别
        enabled_levels = self.filter_widget.get_enabled_levels()

        # 选择保存文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"fliptalk_logs_{timestamp}.txt"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出日志",
            default_filename,
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            success = self._logger.export_logs(file_path, enabled_levels)
            if success:
                QMessageBox.information(
                    self,
                    "导出成功",
                    f"日志已成功导出到:\n{file_path}"
                )
                self.status_label.setText(f"日志已导出到: {os.path.basename(file_path)}")
            else:
                QMessageBox.warning(
                    self,
                    "导出失败",
                    "导出日志时发生错误，请检查文件路径和权限。"
                )

    @Slot(str)
    def search_logs(self, text: str):
        """搜索日志"""
        count = self.log_display.search_text(text)
        self._search_count = count
        self._search_current = 0 if count > 0 else -1

        self.toolbar.update_search_result(count, self._search_current)

        if text and count == 0:
            self.status_label.setText(f"未找到匹配 '{text}' 的日志")
        elif text and count > 0:
            self.status_label.setText(f"找到 {count} 条匹配 '{text}' 的日志")
        else:
            self.status_label.setText("日志系统已就绪")

    @Slot()
    def search_next(self):
        """搜索下一个"""
        if self._search_count > 0:
            self.log_display.go_to_next_search_result()
            self._search_current = (self._search_current + 1) % self._search_count
            self.toolbar.update_search_result(self._search_count, self._search_current)

    @Slot()
    def search_previous(self):
        """搜索上一个"""
        if self._search_count > 0:
            self.log_display.go_to_previous_search_result()
            self._search_current = (self._search_current - 1) % self._search_count
            self.toolbar.update_search_result(self._search_count, self._search_current)

    @Slot()
    def refresh_display(self):
        """刷新显示"""
        # 清除当前显示
        self.log_display.clear_logs()

        # 重新加载符合过滤条件的日志
        enabled_levels = self.filter_widget.get_enabled_levels()
        logs = self._logger.get_logs(enabled_levels)

        for log in logs:
            self.log_display.add_log_entry(log)

        self.refresh_stats()
        self.status_label.setText(f"显示 {len(logs)} 条日志")

    @Slot()
    def refresh_stats(self):
        """刷新统计信息"""
        self.filter_widget.update_stats()

    def closeEvent(self, event):
        """关闭事件"""
        # 停止定时器
        if self._refresh_timer.isActive():
            self._refresh_timer.stop()

        event.accept()
