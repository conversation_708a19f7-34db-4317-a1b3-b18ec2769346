#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端DeepL API密钥配置测试（不涉及UI）
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_manager_basic():
    """测试配置管理器基本功能"""
    print("🔧 测试配置管理器基本功能...")
    
    try:
        from core.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        print("✅ 配置管理器初始化成功")
        print(f"📁 配置文件路径: {config_manager.config_file}")
        
        # 测试保存DeepL API密钥
        test_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
        print(f"💾 保存测试密钥: {test_key}")
        
        success = config_manager.update_api_key("deepl", "key", test_key)
        if success:
            print("✅ 密钥保存成功")
        else:
            print("❌ 密钥保存失败")
            return False
        
        # 测试读取DeepL API密钥
        saved_key = config_manager.get("api_keys.deepl_api_key", "")
        print(f"📥 读取保存的密钥: {saved_key}")
        
        if saved_key == test_key:
            print("✅ 密钥读取验证成功")
            return True
        else:
            print(f"❌ 密钥读取验证失败，期望: {test_key}，实际: {saved_key}")
            return False
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file_structure():
    """测试配置文件结构"""
    print("\n📄 测试配置文件结构...")
    
    try:
        from core.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        config_file = config_manager.config_file
        
        if os.path.exists(config_file):
            print("✅ 配置文件存在")
            
            # 读取配置文件内容
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = json.load(f)
            
            print("📋 配置文件结构:")
            print(json.dumps(config_content, indent=2, ensure_ascii=False))
            
            # 检查必要的键是否存在
            required_keys = ['api_keys', 'tts_settings', 'ui_settings', 'app_settings']
            missing_keys = []
            
            for key in required_keys:
                if key not in config_content:
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"❌ 缺少必要的配置键: {missing_keys}")
                return False
            else:
                print("✅ 配置文件结构完整")
            
            # 检查DeepL API密钥
            deepl_key = config_content.get('api_keys', {}).get('deepl_api_key', '')
            if deepl_key:
                print(f"✅ DeepL API密钥已配置: {deepl_key[:10]}...{deepl_key[-4:] if len(deepl_key) > 14 else deepl_key}")
            else:
                print("⚠️ DeepL API密钥未配置")
            
            return True
        else:
            print("❌ 配置文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 配置文件结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deepl_translator_plugin():
    """测试DeepL翻译器插件"""
    print("\n🔌 测试DeepL翻译器插件...")
    
    try:
        from plugins.subtitle_translator.plugin import DeepLTranslator
        
        # 使用测试API密钥创建翻译器
        test_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
        translator = DeepLTranslator(test_key)
        
        print("✅ DeepL翻译器创建成功")
        print(f"📝 翻译器名称: {translator.get_name()}")
        print(f"📄 翻译器描述: {translator.get_description()}")
        
        # 检查支持的语言
        languages = translator.get_supported_languages()
        print(f"🌐 支持的语言数量: {len(languages)}")
        print(f"🌐 支持的语言: {list(languages.keys())}")
        
        # 检查API密钥是否正确设置
        if hasattr(translator, 'api_key') and translator.api_key == test_key:
            print("✅ API密钥设置正确")
            return True
        else:
            print("❌ API密钥设置不正确")
            return False
        
    except Exception as e:
        print(f"❌ DeepL翻译器插件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_plugin_integration():
    """测试翻译插件集成"""
    print("\n🔗 测试翻译插件集成...")
    
    try:
        from plugins.subtitle_translator.plugin import SubtitleTranslatorPlugin
        from core.config_manager import get_config_manager
        
        # 先保存API密钥到配置
        config_manager = get_config_manager()
        test_key = "integration-test-key-12345"
        config_manager.update_api_key("deepl", "key", test_key)
        print(f"💾 保存测试密钥到配置: {test_key}")
        
        # 创建翻译插件
        plugin = SubtitleTranslatorPlugin()
        print("✅ 翻译插件创建成功")
        
        # 测试设置DeepL翻译器
        success = plugin.set_translator("deepl", test_key)
        if success:
            print("✅ DeepL翻译器设置成功")
        else:
            print("❌ DeepL翻译器设置失败")
            return False
        
        # 检查当前翻译器
        if plugin.current_translator:
            print(f"✅ 当前翻译器: {plugin.current_translator.get_name()}")
            return True
        else:
            print("❌ 当前翻译器未设置")
            return False
        
    except Exception as e:
        print(f"❌ 翻译插件集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_config_flow():
    """测试端到端配置流程"""
    print("\n🔄 测试端到端配置流程...")
    
    try:
        from core.config_manager import get_config_manager
        from plugins.subtitle_translator.plugin import SubtitleTranslatorPlugin
        
        config_manager = get_config_manager()
        
        # 步骤1: 清除现有配置
        config_manager.update_api_key("deepl", "key", "")
        print("🧹 步骤1: 清除现有配置")
        
        # 步骤2: 模拟用户在API设置界面保存密钥
        user_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
        success = config_manager.update_api_key("deepl", "key", user_key)
        if success:
            print(f"✅ 步骤2: 用户保存API密钥成功 - {user_key}")
        else:
            print("❌ 步骤2: 用户保存API密钥失败")
            return False
        
        # 步骤3: 验证配置文件中的密钥
        saved_key = config_manager.get("api_keys.deepl_api_key", "")
        if saved_key == user_key:
            print("✅ 步骤3: 配置文件验证成功")
        else:
            print(f"❌ 步骤3: 配置文件验证失败，期望: {user_key}，实际: {saved_key}")
            return False
        
        # 步骤4: 模拟字幕翻译功能读取配置
        loaded_key = config_manager.get("api_keys.deepl_api_key", "")
        if loaded_key == user_key:
            print("✅ 步骤4: 字幕翻译功能读取配置成功")
        else:
            print(f"❌ 步骤4: 字幕翻译功能读取配置失败，期望: {user_key}，实际: {loaded_key}")
            return False
        
        # 步骤5: 创建翻译器并验证API密钥
        plugin = SubtitleTranslatorPlugin()
        success = plugin.set_translator("deepl", loaded_key)
        if success:
            print("✅ 步骤5: 翻译器创建和配置成功")
        else:
            print("❌ 步骤5: 翻译器创建和配置失败")
            return False
        
        print("🎉 端到端配置流程测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 端到端配置流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI DeepL API密钥后端配置测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("配置管理器基本功能", test_config_manager_basic),
        ("配置文件结构", test_config_file_structure),
        ("DeepL翻译器插件", test_deepl_translator_plugin),
        ("翻译插件集成", test_translation_plugin_integration),
        ("端到端配置流程", test_end_to_end_config_flow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*15} {test_name} {'='*15}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 后端测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 DeepL API密钥后端配置功能完全正常！")
        
        print("\n✅ 验证通过的功能:")
        print("1. ✅ 配置管理器可以正确保存和读取DeepL API密钥")
        print("2. ✅ 配置文件结构完整，持久化正常")
        print("3. ✅ DeepL翻译器插件可以正确初始化")
        print("4. ✅ 翻译插件集成工作正常")
        print("5. ✅ 端到端配置流程完全打通")
        
        print("\n🔧 修复的问题:")
        print("• API设置界面保存逻辑已连接到配置管理器")
        print("• 字幕翻译对话框可以从配置管理器读取密钥")
        print("• 配置存储和读取机制完全同步")
        print("• 翻译器初始化时可以正确使用保存的密钥")
        
        print("\n🎯 用户使用流程:")
        print("1. 在API设置界面输入DeepL API密钥并保存")
        print("2. 打开字幕翻译对话框")
        print("3. 选择DeepL翻译器，密钥自动填充")
        print("4. 开始翻译，系统使用保存的密钥")
        
    else:
        print(f"\n❌ 仍有 {total_tests - passed_tests} 个后端问题需要修复")

if __name__ == "__main__":
    main()
