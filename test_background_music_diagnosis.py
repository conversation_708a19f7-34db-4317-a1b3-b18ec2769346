#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI 背景音乐处理诊断工具
用于检查视频合成中背景音乐缺失的问题
"""

import os
import sys
import glob
from pathlib import Path

def check_voice_separation_output(output_dir):
    """检查人声分离输出"""
    print("🔍 检查人声分离输出...")
    
    voice_separation_dir = os.path.join(output_dir, "voice_separation")
    if not os.path.exists(voice_separation_dir):
        print(f"❌ 人声分离目录不存在: {voice_separation_dir}")
        return None
    
    print(f"✅ 人声分离目录存在: {voice_separation_dir}")
    
    # 列出所有文件
    files = os.listdir(voice_separation_dir)
    print(f"📁 目录中的文件: {files}")
    
    # 查找背景音乐文件
    background_patterns = [
        "*background*.wav", "*background*.mp3",
        "*other*.wav", "*other*.mp3", 
        "*instrumental*.wav", "*instrumental*.mp3"
    ]
    
    background_files = []
    for pattern in background_patterns:
        matches = glob.glob(os.path.join(voice_separation_dir, pattern))
        background_files.extend(matches)
    
    if background_files:
        print(f"🎵 找到背景音乐文件: {background_files}")
        return background_files[0]  # 返回第一个找到的文件
    else:
        print("❌ 未找到背景音乐文件")
        return None

def analyze_audio_file(file_path):
    """分析音频文件"""
    try:
        from pydub import AudioSegment
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return None
        
        file_size = os.path.getsize(file_path)
        print(f"📊 文件大小: {file_size / (1024*1024):.2f} MB")
        
        audio = AudioSegment.from_file(file_path)
        duration = len(audio) / 1000.0  # 转换为秒
        volume_db = audio.dBFS
        
        print(f"📊 音频信息:")
        print(f"   - 时长: {duration:.2f}s")
        print(f"   - 音量: {volume_db:.1f}dB")
        print(f"   - 声道数: {audio.channels}")
        print(f"   - 采样率: {audio.frame_rate}Hz")
        
        # 检查是否为静音
        if volume_db == float('-inf'):
            print("⚠️ 音频为静音")
            return False
        elif volume_db < -30:
            print("⚠️ 音频音量过低")
            return False
        else:
            print("✅ 音频质量正常")
            return True
            
    except ImportError:
        print("❌ 缺少 pydub 库，无法分析音频")
        return None
    except Exception as e:
        print(f"❌ 分析音频失败: {e}")
        return None

def check_synthesis_process(output_dir):
    """检查合成过程"""
    print("\n🔍 检查合成过程...")
    
    synthesis_dir = os.path.join(output_dir, "synthesis")
    if not os.path.exists(synthesis_dir):
        print(f"❌ 合成目录不存在: {synthesis_dir}")
        return
    
    print(f"✅ 合成目录存在: {synthesis_dir}")
    
    # 检查合成目录中的文件
    files = os.listdir(synthesis_dir)
    print(f"📁 合成目录中的文件: {files}")
    
    # 查找配音合并文件
    voiceover_files = [f for f in files if "combined_voiceover" in f]
    if voiceover_files:
        print(f"🎤 找到配音合并文件: {voiceover_files}")
        for file in voiceover_files:
            file_path = os.path.join(synthesis_dir, file)
            print(f"\n分析配音文件: {file}")
            analyze_audio_file(file_path)
    
    # 查找背景音乐混合文件
    mixed_files = [f for f in files if "mixed_audio_with_background" in f]
    if mixed_files:
        print(f"🎵 找到背景音乐混合文件: {mixed_files}")
        for file in mixed_files:
            file_path = os.path.join(synthesis_dir, file)
            print(f"\n分析混合文件: {file}")
            analyze_audio_file(file_path)
    else:
        print("❌ 未找到背景音乐混合文件")
    
    # 查找最终视频文件
    video_files = [f for f in files if f.endswith(('.mp4', '.avi', '.mov'))]
    if video_files:
        print(f"🎬 找到视频文件: {video_files}")
    else:
        print("❌ 未找到视频文件")

def check_ffmpeg_command():
    """检查FFmpeg命令可用性"""
    print("\n🔍 检查FFmpeg...")
    
    try:
        import subprocess
        result = subprocess.run(["ffmpeg", "-version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg 可用")
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"📋 版本: {version_line}")
        else:
            print("❌ FFmpeg 不可用")
    except FileNotFoundError:
        print("❌ FFmpeg 未安装或不在PATH中")
    except Exception as e:
        print(f"❌ 检查FFmpeg失败: {e}")

def simulate_background_music_mixing(output_dir):
    """模拟背景音乐混合过程"""
    print("\n🧪 模拟背景音乐混合过程...")
    
    # 查找背景音乐文件
    background_file = check_voice_separation_output(output_dir)
    if not background_file:
        print("❌ 无法进行混合测试：未找到背景音乐文件")
        return
    
    # 查找配音文件
    synthesis_dir = os.path.join(output_dir, "synthesis")
    voiceover_files = []
    if os.path.exists(synthesis_dir):
        files = os.listdir(synthesis_dir)
        voiceover_files = [f for f in files if "combined_voiceover" in f]
    
    if not voiceover_files:
        print("❌ 无法进行混合测试：未找到配音文件")
        return
    
    voiceover_file = os.path.join(synthesis_dir, voiceover_files[0])
    
    print(f"🎵 背景音乐: {background_file}")
    print(f"🎤 配音文件: {voiceover_file}")
    
    # 分析两个文件
    print("\n分析背景音乐:")
    bg_valid = analyze_audio_file(background_file)
    
    print("\n分析配音文件:")
    vo_valid = analyze_audio_file(voiceover_file)
    
    if bg_valid and vo_valid:
        print("\n✅ 两个音频文件都有效，可以进行混合")
        
        # 尝试简单混合测试
        try:
            from pydub import AudioSegment
            
            bg_audio = AudioSegment.from_file(background_file)
            vo_audio = AudioSegment.from_wav(voiceover_file)
            
            print(f"🔧 配音时长: {len(vo_audio)/1000:.2f}s")
            print(f"🔧 背景音乐时长: {len(bg_audio)/1000:.2f}s")
            
            # 调整长度
            if len(bg_audio) > len(vo_audio):
                bg_audio = bg_audio[:len(vo_audio)]
                print("🔧 背景音乐已截取")
            elif len(bg_audio) < len(vo_audio):
                repeat_times = int(len(vo_audio) / len(bg_audio)) + 1
                bg_audio = bg_audio * repeat_times
                bg_audio = bg_audio[:len(vo_audio)]
                print(f"🔧 背景音乐已循环 {repeat_times} 次")
            
            # 调整音量
            bg_audio = bg_audio - 8  # 降低8dB
            
            # 混合
            mixed = vo_audio.overlay(bg_audio)
            
            print(f"✅ 混合测试成功")
            print(f"📊 混合后音频: 时长={len(mixed)/1000:.2f}s, 音量={mixed.dBFS:.1f}dB")
            
        except Exception as e:
            print(f"❌ 混合测试失败: {e}")
    else:
        print("❌ 音频文件无效，无法进行混合")

def main():
    """主函数"""
    print("🚀 FlipTalk AI 背景音乐处理诊断工具")
    print("=" * 60)
    
    # 获取输出目录
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    else:
        output_dir = input("请输入输出目录路径 (默认: output): ").strip()
        if not output_dir:
            output_dir = "output"
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    print(f"📂 检查输出目录: {output_dir}")
    
    # 执行各项检查
    check_voice_separation_output(output_dir)
    check_synthesis_process(output_dir)
    check_ffmpeg_command()
    simulate_background_music_mixing(output_dir)
    
    print("\n" + "=" * 60)
    print("🔍 诊断完成")
    
    print("\n💡 问题排查建议:")
    print("1. 确保已执行人声分离并生成背景音乐文件")
    print("2. 检查背景音乐文件是否有效（非静音、时长合理）")
    print("3. 确保在视频合成时勾选了'保留背景音乐'选项")
    print("4. 检查FFmpeg是否正确安装和配置")
    print("5. 查看控制台输出中的错误信息")

if __name__ == "__main__":
    main()
