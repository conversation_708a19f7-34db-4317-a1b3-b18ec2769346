#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无边框窗口效果
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 测试无边框窗口效果")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import FlipTalkMainWindow
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("📱 创建无边框主窗口...")
        main_window = FlipTalkMainWindow()
        
        # 显示主窗口
        main_window.show()
        print("🖥️ 无边框主窗口已显示")
        
        print("\n✅ 无边框窗口特色:")
        print("1. ❌ 移除了系统标题栏")
        print("2. ❌ 移除了窗口控制按钮外框")
        print("3. ✅ 支持鼠标拖拽移动窗口")
        print("4. ✅ 保持窗口功能完整")
        print("5. ✅ 现代化无边框设计")
        
        print("\n🖱️ 操作说明:")
        print("• 在窗口任意位置按住鼠标左键可拖拽移动窗口")
        print("• 窗口没有系统标题栏和控制按钮")
        print("• 使用应用内的功能进行操作")
        print("• 关闭窗口需要使用应用内的退出功能")
        
        print("\n🎨 视觉效果:")
        print("• 完全无边框设计")
        print("• 移除了红色和绿色控制按钮的外框")
        print("• 更加现代化和简洁的外观")
        print("• 与应用内容完美融合")
        
        print("\n⚠️ 注意事项:")
        print("• 窗口拖拽：在任意空白区域拖拽")
        print("• 关闭窗口：使用键盘快捷键或应用内功能")
        print("• 最小化：需要通过应用内功能实现")
        print("• 调整大小：当前为固定尺寸窗口")
        
        print("\n⌨️ 按 Ctrl+C 退出测试")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
