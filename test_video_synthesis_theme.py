#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频合成对话框深色主题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dark_theme():
    """测试深色主题"""
    print("🎨 测试视频合成对话框深色主题...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.video_synthesis_dialog import VideoSynthesisDialog
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = VideoSynthesisDialog()
        
        # 检查样式表是否包含深色主题元素
        stylesheet = dialog.styleSheet()
        
        dark_theme_elements = {
            "深色背景": "#1E1E1E",
            "蓝色主题色": "#4A9EFF", 
            "深色组件背景": "#2A2A2A",
            "边框颜色": "#3A3A3A",
            "白色文字": "#FFFFFF",
            "渐变按钮": "qlineargradient",
            "圆角设计": "border-radius"
        }
        
        missing_elements = []
        for element_name, element_code in dark_theme_elements.items():
            if element_code not in stylesheet:
                missing_elements.append(element_name)
        
        if missing_elements:
            print(f"❌ 缺少深色主题元素: {', '.join(missing_elements)}")
            return False
        else:
            print("✅ 深色主题样式完整")
        
        # 检查组件样式
        components_to_check = [
            ("QDialog", "对话框主体"),
            ("QGroupBox", "分组框"),
            ("QPushButton", "按钮"),
            ("QListWidget", "列表组件"),
            ("QTextEdit", "文本编辑器"),
            ("QCheckBox", "复选框"),
            ("QSpinBox", "数字输入框"),
            ("QComboBox", "下拉框"),
            ("QProgressBar", "进度条")
        ]
        
        styled_components = []
        for component, name in components_to_check:
            if component in stylesheet:
                styled_components.append(name)
        
        print(f"✅ 已设置样式的组件: {', '.join(styled_components)}")
        
        # 显示对话框进行视觉测试
        print("🖼️ 显示对话框进行视觉测试...")
        dialog.show()
        
        # 等待用户确认
        print("请检查对话框的深色主题效果，按任意键继续...")
        input()
        
        dialog.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试深色主题失败: {e}")
        return False

def test_color_consistency():
    """测试颜色一致性"""
    print("\n🎨 测试颜色一致性...")
    
    try:
        # 读取样式表内容
        with open("ui/video_synthesis_dialog.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查主要颜色的使用
        main_colors = {
            "主背景色": "#1E1E1E",
            "组件背景色": "#2A2A2A", 
            "边框颜色": "#3A3A3A",
            "主题蓝色": "#4A9EFF",
            "白色文字": "#FFFFFF",
            "错误红色": "#FF6B6B",
            "灰色文字": "#CCCCCC"
        }
        
        color_usage = {}
        for color_name, color_code in main_colors.items():
            count = content.count(color_code)
            color_usage[color_name] = count
            print(f"  {color_name} ({color_code}): 使用 {count} 次")
        
        # 检查是否有一致的颜色使用
        if color_usage["主题蓝色"] > 0:
            print("✅ 主题蓝色使用一致")
        else:
            print("❌ 主题蓝色使用不一致")
            return False
        
        if color_usage["主背景色"] > 0:
            print("✅ 深色背景使用一致")
        else:
            print("❌ 深色背景使用不一致")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试颜色一致性失败: {e}")
        return False

def test_ui_component_colors():
    """测试UI组件颜色"""
    print("\n🎨 测试UI组件颜色...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.video_synthesis_dialog import VideoSynthesisDialog
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = VideoSynthesisDialog()
        
        # 检查标签的初始颜色
        initial_colors = {
            "视频文件标签": dialog.video_file_label.styleSheet(),
            "音频目录标签": dialog.audio_dir_label.styleSheet(),
            "字幕文件标签": dialog.subtitle_file_label.styleSheet(),
            "背景音乐标签": dialog.bg_music_label.styleSheet(),
            "输出目录标签": dialog.output_dir_label.styleSheet(),
            "状态标签": dialog.status_label.styleSheet()
        }
        
        print("📋 组件初始颜色设置:")
        for component, style in initial_colors.items():
            if "#FF6B6B" in style:  # 错误状态颜色
                print(f"  ✅ {component}: 错误状态颜色 (红色)")
            elif "#4A9EFF" in style:  # 成功状态颜色
                print(f"  ✅ {component}: 成功状态颜色 (蓝色)")
            elif "#CCCCCC" in style:  # 默认状态颜色
                print(f"  ✅ {component}: 默认状态颜色 (灰色)")
            else:
                print(f"  ⚠️ {component}: 未知颜色设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试UI组件颜色失败: {e}")
        return False

def main():
    """主函数"""
    print("🎨 视频合成对话框深色主题测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("深色主题", test_dark_theme),
        ("颜色一致性", test_color_consistency),
        ("UI组件颜色", test_ui_component_colors)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 深色主题完美适配！")
        print("\n🎨 主题特色:")
        print("1. ✅ 深色背景 (#1E1E1E) - 护眼舒适")
        print("2. ✅ 蓝色主题 (#4A9EFF) - 与FlipTalk AI一致")
        print("3. ✅ 渐变按钮 - 现代化设计")
        print("4. ✅ 圆角设计 - 优雅美观")
        print("5. ✅ 状态颜色 - 清晰的视觉反馈")
        print("6. ✅ 组件统一 - 整体协调")
        
        print("\n💡 颜色方案:")
        print("  🔵 主题蓝色: #4A9EFF (成功状态、链接、主要按钮)")
        print("  🔴 错误红色: #FF6B6B (未选择状态、错误提示)")
        print("  ⚪ 白色文字: #FFFFFF (主要文本)")
        print("  🔘 灰色文字: #CCCCCC (次要文本)")
        print("  ⚫ 深色背景: #1E1E1E (主背景)")
        print("  🔲 组件背景: #2A2A2A (卡片、输入框)")
        
    else:
        print(f"\n❌ 深色主题有 {total_tests - passed_tests} 个问题需要修复")

if __name__ == "__main__":
    main()
