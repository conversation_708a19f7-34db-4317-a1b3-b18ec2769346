#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合并后的工具栏和统计标签布局
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 测试合并后的工具栏和统计标签布局")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.log_area import LogArea
        from core.logger import get_logger, log_info, log_warning, log_error, log_debug
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("📱 创建LogArea...")
        log_area = LogArea()
        
        # 添加测试日志
        logger = get_logger()
        
        # 添加不同数量的各级别日志
        for i in range(12):
            log_info("测试", f"信息日志 {i+1}")
        
        for i in range(6):
            log_warning("测试", f"警告日志 {i+1}")
        
        for i in range(4):
            log_error("测试", f"错误日志 {i+1}")
        
        for i in range(3):
            log_debug("测试", f"调试日志 {i+1}")
        
        print("📝 已添加测试日志:")
        print("   - 信息: 12条")
        print("   - 警告: 6条")
        print("   - 错误: 4条")
        print("   - 调试: 3条")
        print("   - 总计: 25条")
        
        # 显示LogArea
        log_area.show()
        print("🖥️ LogArea已显示")
        
        print("\n✅ 合并布局特色:")
        print("1. 📊 统计标签和工具栏在同一行")
        print("2. 🎯 左侧显示统计标签")
        print("3. 🔧 右侧显示工具栏功能")
        print("4. 📏 更紧凑的垂直空间利用")
        print("5. 🎨 统一的视觉风格")
        
        print("\n🎯 布局说明:")
        print("• 左侧: 总计、错误、警告、信息、调试标签")
        print("• 分隔线: 视觉分隔统计和功能区域")
        print("• 重置按钮: 快速重置所有过滤器")
        print("• 右侧: 搜索框、导航按钮、操作按钮")
        
        print("\n🖱️ 交互功能:")
        print("• 点击统计标签切换过滤状态")
        print("• 使用搜索框查找日志内容")
        print("• 点击清除/导出按钮操作日志")
        print("• 点击重置按钮恢复所有显示")
        
        print("\n📐 空间优化:")
        print("• 原来两行 → 现在一行")
        print("• 节省垂直空间约50px")
        print("• 日志显示区域更大")
        print("• 界面更加紧凑")
        
        print("\n⌨️ 按 Ctrl+C 退出测试")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
