#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断DeepL API密钥配置问题
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_config_storage():
    """诊断配置存储机制"""
    print("🔍 诊断配置存储机制...")
    
    try:
        from core.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        print(f"✅ 配置管理器初始化成功")
        print(f"📁 配置文件路径: {config_manager.config_file}")
        
        # 检查配置文件是否存在
        if os.path.exists(config_manager.config_file):
            print("✅ 配置文件存在")
            
            # 读取配置文件内容
            with open(config_manager.config_file, 'r', encoding='utf-8') as f:
                config_content = json.load(f)
            
            print("📋 当前配置文件内容:")
            print(json.dumps(config_content, indent=2, ensure_ascii=False))
            
            # 检查DeepL API密钥
            deepl_key = config_content.get('api_keys', {}).get('deepl_api_key', '')
            if deepl_key:
                print(f"✅ DeepL API密钥已配置: {deepl_key[:10]}...{deepl_key[-4:] if len(deepl_key) > 14 else deepl_key}")
            else:
                print("❌ DeepL API密钥未配置或为空")
        else:
            print("❌ 配置文件不存在")
            
        # 测试通过配置管理器读取
        deepl_key_from_manager = config_manager.get("api_keys.deepl_api_key", "")
        print(f"🔧 通过配置管理器读取的DeepL密钥: {deepl_key_from_manager[:10] + '...' + deepl_key_from_manager[-4:] if len(deepl_key_from_manager) > 14 else deepl_key_from_manager}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置存储诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_save():
    """测试配置保存功能"""
    print("\n💾 测试配置保存功能...")
    
    try:
        from core.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        # 测试保存DeepL密钥
        test_key = "test-deepl-key-12345"
        print(f"🧪 测试保存DeepL密钥: {test_key}")
        
        # 使用update_api_key方法
        success = config_manager.update_api_key("deepl", "key", test_key)
        if success:
            print("✅ 配置保存成功")
        else:
            print("❌ 配置保存失败")
        
        # 验证保存结果
        saved_key = config_manager.get("api_keys.deepl_api_key", "")
        if saved_key == test_key:
            print("✅ 配置保存验证成功")
        else:
            print(f"❌ 配置保存验证失败，期望: {test_key}，实际: {saved_key}")
        
        # 恢复原始配置（如果有的话）
        print("🔄 恢复原始配置...")
        config_manager.update_api_key("deepl", "key", "")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_settings_integration():
    """测试API设置界面集成"""
    print("\n🎛️ 测试API设置界面集成...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.api_settings import APISettingsWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建API设置组件
        api_settings = APISettingsWidget()
        print("✅ API设置组件创建成功")
        
        # 检查DeepL设置区域是否存在
        # 这里需要检查create_modern_setting_section方法的实现
        print("⚠️ API设置界面的保存逻辑需要修复")
        print("   - 当前保存按钮只打印信息，没有实际保存到配置管理器")
        print("   - 需要连接到config_manager.update_api_key方法")
        
        return True
        
    except Exception as e:
        print(f"❌ API设置界面集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_dialog_integration():
    """测试字幕翻译对话框集成"""
    print("\n📝 测试字幕翻译对话框集成...")
    
    try:
        # 检查字幕翻译对话框是否从配置管理器读取密钥
        print("🔍 检查字幕翻译对话框的密钥读取逻辑...")
        
        # 查看源码发现：字幕翻译对话框只从UI输入框读取API密钥
        # 没有从配置管理器自动加载已保存的密钥
        print("❌ 发现问题：字幕翻译对话框没有从配置管理器加载已保存的DeepL密钥")
        print("   - 对话框只从api_key_edit输入框读取密钥")
        print("   - 没有在初始化时从配置管理器加载已保存的密钥")
        print("   - 需要在对话框初始化时自动填充已保存的密钥")
        
        return False
        
    except Exception as e:
        print(f"❌ 字幕翻译对话框集成测试失败: {e}")
        return False

def test_deepl_api_format():
    """测试DeepL API密钥格式"""
    print("\n🔑 测试DeepL API密钥格式...")
    
    # 从图片中看到的密钥
    test_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
    
    print(f"📋 测试密钥: {test_key}")
    print(f"📏 密钥长度: {len(test_key)}")
    
    # DeepL API密钥格式验证
    import re
    
    # DeepL API密钥通常是UUID格式，以:fx结尾（免费版）或不带后缀（专业版）
    deepl_pattern = r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(:fx)?$'
    
    if re.match(deepl_pattern, test_key):
        print("✅ DeepL API密钥格式正确")
    else:
        print("❌ DeepL API密钥格式可能不正确")
        print("   - DeepL API密钥应该是UUID格式")
        print("   - 免费版密钥以':fx'结尾")
        print("   - 专业版密钥不带后缀")
    
    return True

def provide_solution():
    """提供解决方案"""
    print("\n🔧 问题诊断和解决方案:")
    print("=" * 60)
    
    print("\n❌ 发现的问题:")
    print("1. API设置界面的保存按钮没有实际保存功能")
    print("2. 字幕翻译对话框没有从配置管理器加载已保存的密钥")
    print("3. 配置存储和读取机制存在断层")
    
    print("\n✅ 解决方案:")
    print("1. 修复API设置界面的保存逻辑")
    print("   - 连接保存按钮到config_manager.update_api_key方法")
    print("   - 添加保存成功/失败的用户反馈")
    
    print("2. 修复字幕翻译对话框的密钥加载")
    print("   - 在对话框初始化时从配置管理器加载已保存的密钥")
    print("   - 自动填充到API密钥输入框")
    
    print("3. 添加配置同步机制")
    print("   - 确保API设置和翻译功能使用相同的配置源")
    print("   - 添加配置变更通知机制")
    
    print("4. 增强调试和错误处理")
    print("   - 添加详细的日志输出")
    print("   - 提供更清晰的错误提示")

def main():
    """主函数"""
    print("🚀 FlipTalk AI DeepL API密钥配置诊断")
    print("=" * 60)
    
    # 运行诊断测试
    tests = [
        ("配置存储机制", diagnose_config_storage),
        ("配置保存功能", test_config_save),
        ("API设置界面集成", test_api_settings_integration),
        ("字幕翻译对话框集成", test_translation_dialog_integration),
        ("DeepL API密钥格式", test_deepl_api_format)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 诊断结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    # 提供解决方案
    provide_solution()

if __name__ == "__main__":
    main()
