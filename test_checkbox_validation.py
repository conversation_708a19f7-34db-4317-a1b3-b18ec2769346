#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试复选框验证逻辑是否会导致自动取消勾选
"""

import os
import sys

class MockFlipTalkUI:
    """模拟FlipTalk UI类"""
    
    def __init__(self):
        # 模拟复选框状态
        self.composition_checkboxes = {
            'keep_background_music': MockCheckbox(False)  # 初始未勾选
        }
        
        # 模拟参数面板
        self.parameter_panel = MockParameterPanel()
    
    def on_background_music_checkbox_changed(self, state):
        """处理保留背景音乐复选框状态变化"""
        try:
            is_checked = state == 2  # Qt.Checked = 2
            print(f"🎵 保留背景音乐选项: {'已勾选' if is_checked else '已取消'}")

            if is_checked:
                # 检查是否满足保留背景音乐的条件
                print("🔍 检查背景音乐保留条件...")
                
                # 模拟获取主窗口实例
                main_window = self  # 在这个测试中，自己就是主窗口
                if main_window and hasattr(main_window, 'validate_background_music_requirements'):
                    validation_result = main_window.validate_background_music_requirements()
                    print(f"📋 验证结果: {validation_result}")

                    if not validation_result['valid']:
                        # 条件不满足，显示提示并取消勾选
                        print("❌ 验证失败，自动取消勾选")
                        print(f"❌ 失败原因: {validation_result['message']}")
                        
                        # 取消勾选
                        self.composition_checkboxes['keep_background_music'].setChecked(False)
                        print("🔄 复选框已自动取消勾选")
                        return False
                    else:
                        print("✅ 背景音乐保留条件检查通过")
                        return True
                else:
                    print("❌ 无法获取主窗口实例或验证方法")
                    return False
            else:
                print("📋 用户取消勾选背景音乐选项")
                return True

        except Exception as e:
            print(f"❌ 处理背景音乐复选框状态变化失败: {e}")
            return False
    
    def validate_background_music_requirements(self):
        """验证保留背景音乐的要求"""
        try:
            print("🔍 验证背景音乐保留要求...")
            
            # 检查是否启用了人声分离
            voice_separation_enabled = self.check_voice_separation_enabled()
            print(f"📋 人声分离启用状态: {voice_separation_enabled}")
            
            if not voice_separation_enabled:
                return {
                    'valid': False,
                    'message': (
                        "要保留背景音乐，需要先启用人声分离功能。\n\n"
                        "请在首页选项设置中勾选\"人声分离\"，\n"
                        "并完成音频的人声分离处理。"
                    )
                }

            # 检查背景音乐文件是否存在
            background_music_path = self.get_background_music_path()
            print(f"📂 背景音乐路径: {background_music_path}")
            
            if not background_music_path or not os.path.exists(background_music_path):
                return {
                    'valid': False,
                    'message': (
                        "未找到背景音乐文件。\n\n"
                        "请先执行人声分离操作，生成背景音乐文件后\n"
                        "再尝试保留背景音乐。"
                    )
                }

            # 验证背景音乐文件的有效性
            if not self.validate_background_music_file(background_music_path):
                return {
                    'valid': False,
                    'message': (
                        "背景音乐文件损坏或无效。\n\n"
                        "请重新执行人声分离操作。"
                    )
                }

            print("✅ 背景音乐保留条件检查通过")
            return {
                'valid': True,
                'message': "背景音乐保留条件满足"
            }

        except Exception as e:
            print(f"❌ 验证背景音乐要求失败: {e}")
            return {
                'valid': False,
                'message': f"验证过程中出现错误: {str(e)}"
            }
    
    def check_voice_separation_enabled(self):
        """检查是否启用了人声分离功能"""
        try:
            # 检查参数设置面板中的人声分离选项
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'voice_separation_checkbox'):
                checkbox_result = self.parameter_panel.voice_separation_checkbox.isChecked()
                print(f"📋 参数面板人声分离复选框: {checkbox_result}")
                if checkbox_result:
                    return True

            # 如果没有找到复选框，检查是否有人声分离的输出文件
            voice_separation_dir = self.get_voice_separation_output_dir()
            if voice_separation_dir and os.path.exists(voice_separation_dir):
                # 检查目录中是否有分离后的文件
                files = os.listdir(voice_separation_dir)
                background_files = [f for f in files if 'background' in f.lower() or 'other' in f.lower()]
                return len(background_files) > 0

            return False

        except Exception as e:
            print(f"❌ 检查人声分离状态失败: {e}")
            return False
    
    def get_voice_separation_output_dir(self):
        """获取人声分离输出目录"""
        return "output/voice_separation"
    
    def get_background_music_path(self):
        """获取背景音乐文件路径"""
        return r"J:\MyAi\03 FlipTalk Ai\output\voice_separation\Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav"
    
    def validate_background_music_file(self, file_path):
        """验证背景音乐文件的有效性"""
        try:
            if not os.path.exists(file_path):
                return False

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size < 1024:  # 小于1KB认为无效
                return False

            # 尝试使用pydub验证音频文件
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(file_path)

                # 检查音频长度
                if len(audio) < 1000:  # 小于1秒认为无效
                    return False

                # 检查音频是否为静音
                if audio.dBFS == float('-inf'):
                    return False

                return True

            except ImportError:
                return True  # 如果没有pydub，只检查文件存在和大小
            except Exception as e:
                return False

        except Exception as e:
            return False

class MockParameterPanel:
    """模拟参数面板"""
    def __init__(self):
        self.voice_separation_checkbox = MockCheckbox(True)  # 模拟已启用人声分离

class MockCheckbox:
    """模拟复选框"""
    def __init__(self, checked=False):
        self._checked = checked
    
    def isChecked(self):
        return self._checked
    
    def setChecked(self, checked):
        print(f"🔄 复选框状态变更: {self._checked} -> {checked}")
        self._checked = checked

def test_checkbox_scenarios():
    """测试不同的复选框场景"""
    print("🧪 测试复选框验证场景")
    print("=" * 50)
    
    ui = MockFlipTalkUI()
    
    # 场景1: 用户勾选复选框（正常情况）
    print("\n📋 场景1: 用户勾选复选框（所有条件满足）")
    ui.parameter_panel.voice_separation_checkbox.setChecked(True)  # 确保人声分离已启用
    
    # 模拟用户勾选
    ui.composition_checkboxes['keep_background_music'].setChecked(True)
    result1 = ui.on_background_music_checkbox_changed(2)  # Qt.Checked = 2
    
    final_state1 = ui.composition_checkboxes['keep_background_music'].isChecked()
    print(f"🔍 最终复选框状态: {final_state1}")
    print(f"🔍 处理结果: {result1}")
    
    # 场景2: 人声分离未启用的情况
    print("\n📋 场景2: 人声分离未启用")
    ui2 = MockFlipTalkUI()
    ui2.parameter_panel.voice_separation_checkbox.setChecked(False)  # 人声分离未启用
    
    # 模拟用户勾选
    ui2.composition_checkboxes['keep_background_music'].setChecked(True)
    result2 = ui2.on_background_music_checkbox_changed(2)
    
    final_state2 = ui2.composition_checkboxes['keep_background_music'].isChecked()
    print(f"🔍 最终复选框状态: {final_state2}")
    print(f"🔍 处理结果: {result2}")
    
    # 场景3: 背景音乐文件不存在的情况
    print("\n📋 场景3: 背景音乐文件不存在")
    ui3 = MockFlipTalkUI()
    ui3.parameter_panel.voice_separation_checkbox.setChecked(True)
    
    # 修改背景音乐路径为不存在的文件
    original_get_path = ui3.get_background_music_path
    ui3.get_background_music_path = lambda: "nonexistent_file.wav"
    
    # 模拟用户勾选
    ui3.composition_checkboxes['keep_background_music'].setChecked(True)
    result3 = ui3.on_background_music_checkbox_changed(2)
    
    final_state3 = ui3.composition_checkboxes['keep_background_music'].isChecked()
    print(f"🔍 最终复选框状态: {final_state3}")
    print(f"🔍 处理结果: {result3}")
    
    return final_state1, final_state2, final_state3

def main():
    """主函数"""
    print("🚀 复选框验证逻辑测试")
    print("=" * 60)
    
    state1, state2, state3 = test_checkbox_scenarios()
    
    print("\n" + "=" * 60)
    print("🔍 测试结论:")
    
    print(f"📋 场景1（正常情况）: 复选框保持勾选 = {state1}")
    print(f"📋 场景2（人声分离未启用）: 复选框被取消 = {not state2}")
    print(f"📋 场景3（背景音乐文件不存在）: 复选框被取消 = {not state3}")
    
    if not state2 or not state3:
        print("\n💡 发现问题!")
        print("❌ 在某些验证失败的情况下，复选框会被自动取消勾选")
        print("❌ 这可能导致用户以为勾选了，但实际上被程序自动取消了")
        print("\n🔧 建议解决方案:")
        print("1. 在UI中显示更明显的验证失败提示")
        print("2. 在视频合成前再次检查复选框状态")
        print("3. 添加更详细的日志输出")
    else:
        print("\n✅ 复选框验证逻辑正常")

if __name__ == "__main__":
    main()
