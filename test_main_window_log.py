#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主窗口中的日志页面显示
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_window_log_area():
    """测试主窗口中的LogArea"""
    print("🏠 测试主窗口中的LogArea...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import FlipTalkMainWindow
        from core.logger import get_logger, log_info, log_warning, log_error
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("📱 创建主窗口...")
        main_window = FlipTalkMainWindow()
        
        # 检查LogArea是否正确创建
        if hasattr(main_window, 'log_area'):
            print("✅ 主窗口包含log_area属性")
            
            # 检查LogArea类型
            log_area_type = type(main_window.log_area).__name__
            print(f"📋 log_area类型: {log_area_type}")
            
            # 检查是否是真正的LogArea还是占位符
            if log_area_type == "LogArea":
                # 检查是否有真正的LogArea方法
                if hasattr(main_window.log_area, '_logger'):
                    print("✅ 这是真正的LogArea（包含_logger属性）")
                else:
                    print("⚠️ 这可能是占位符LogArea（缺少_logger属性）")
            else:
                print(f"❌ 意外的log_area类型: {log_area_type}")
            
            # 检查LogArea大小
            size = main_window.log_area.size()
            print(f"📏 log_area大小: {size.width()} x {size.height()}")
            
            # 添加测试日志
            logger = get_logger()
            log_info("MainWindowTest", "主窗口日志页面测试开始")
            log_warning("MainWindowTest", "这是一个警告信息")
            log_error("MainWindowTest", "这是一个错误信息")
            
            print("📝 已添加测试日志")
            
            # 显示主窗口
            main_window.show()
            print("🖥️ 主窗口已显示")
            
            # 手动切换到日志页面
            print("🔄 切换到日志页面...")
            main_window.on_page_changed("运行日志")
            
            # 检查日志区域是否可见
            if main_window.log_area.isVisible():
                print("✅ 日志区域已显示")
            else:
                print("❌ 日志区域未显示")
                
                # 尝试手动显示
                print("🔧 尝试手动显示日志区域...")
                main_window.log_area.show()
                
                if main_window.log_area.isVisible():
                    print("✅ 手动显示成功")
                else:
                    print("❌ 手动显示也失败")
            
            # 检查布局
            if hasattr(main_window, 'right_area_layout'):
                layout_count = main_window.right_area_layout.count()
                print(f"📦 右侧区域布局包含 {layout_count} 个组件")
                
                # 检查LogArea是否在布局中
                for i in range(layout_count):
                    item = main_window.right_area_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        widget_type = type(widget).__name__
                        is_visible = widget.isVisible()
                        print(f"  📋 组件 {i}: {widget_type} (可见: {is_visible})")
                        
                        if widget == main_window.log_area:
                            print(f"    ✅ 找到LogArea在布局位置 {i}")
            
        else:
            print("❌ 主窗口缺少log_area属性")
            return False
        
        print("\n💡 请在主窗口中点击'运行日志'按钮查看效果")
        print("⌨️ 按 Ctrl+C 退出测试")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口LogArea测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_page_method():
    """测试日志页面显示方法"""
    print("\n🔍 测试日志页面显示方法...")
    
    try:
        from ui.fliptalk_ui import FlipTalkMainWindow
        import inspect
        
        # 检查show_log_page方法
        if hasattr(FlipTalkMainWindow, 'show_log_page'):
            print("✅ 主窗口包含show_log_page方法")
            
            # 获取方法源码
            source = inspect.getsource(FlipTalkMainWindow.show_log_page)
            print("📋 show_log_page方法内容:")
            print("=" * 40)
            print(source)
            print("=" * 40)
            
        else:
            print("❌ 主窗口缺少show_log_page方法")
            return False
        
        # 检查on_page_changed方法
        if hasattr(FlipTalkMainWindow, 'on_page_changed'):
            print("✅ 主窗口包含on_page_changed方法")
            
            # 检查是否包含运行日志的处理
            source = inspect.getsource(FlipTalkMainWindow.on_page_changed)
            if "运行日志" in source:
                print("✅ on_page_changed方法包含运行日志处理")
            else:
                print("❌ on_page_changed方法缺少运行日志处理")
                return False
        else:
            print("❌ 主窗口缺少on_page_changed方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 日志页面方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI 主窗口日志页面测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("日志页面方法", test_log_page_method),
        ("主窗口LogArea", test_main_window_log_area)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")

if __name__ == "__main__":
    main()
