#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI中的背景音乐逻辑
"""

import os
import sys

# 模拟UI类的背景音乐相关方法
class MockFlipTalkUI:
    def __init__(self):
        # 模拟复选框状态
        self.composition_checkboxes = {
            'keep_background_music': MockCheckbox(True)  # 模拟已勾选状态
        }
    
    def get_voice_separation_output_dir(self):
        """获取人声分离输出目录"""
        try:
            # 模拟实际逻辑
            output_dir = "output"
            return os.path.join(output_dir, "voice_separation")
        except Exception as e:
            print(f"❌ 获取人声分离输出目录失败: {e}")
            return None
    
    def get_background_music_path(self):
        """获取背景音乐文件路径"""
        try:
            print("🔍 开始获取背景音乐文件路径...")
            voice_separation_dir = self.get_voice_separation_output_dir()
            print(f"📂 人声分离目录: {voice_separation_dir}")
            
            if not voice_separation_dir:
                print("❌ 人声分离目录路径为空")
                return None
                
            if not os.path.exists(voice_separation_dir):
                print(f"❌ 人声分离目录不存在: {voice_separation_dir}")
                return None

            # 查找背景音乐文件（可能的命名模式）
            possible_patterns = [
                "*background*.wav",
                "*background*.mp3",
                "*other*.wav",
                "*other*.mp3",
                "*instrumental*.wav",
                "*instrumental*.mp3"
            ]

            print(f"🔍 搜索背景音乐文件，模式: {possible_patterns}")
            
            import glob
            all_files = os.listdir(voice_separation_dir)
            print(f"📁 目录中的所有文件: {all_files}")
            
            for pattern in possible_patterns:
                search_path = os.path.join(voice_separation_dir, pattern)
                print(f"🔍 搜索模式: {search_path}")
                files = glob.glob(search_path)
                print(f"📋 匹配的文件: {files}")
                
                if files:
                    # 返回最新的文件
                    latest_file = max(files, key=os.path.getmtime)
                    print(f"🎵 找到背景音乐文件: {latest_file}")
                    return latest_file

            print("❌ 未找到背景音乐文件")
            return None

        except Exception as e:
            print(f"❌ 获取背景音乐文件路径失败: {e}")
            return None
    
    def check_background_music_option(self):
        """检查背景音乐选项状态"""
        print("🔍 检查背景音乐保留选项...")
        
        has_checkboxes = hasattr(self, 'composition_checkboxes')
        has_bg_checkbox = has_checkboxes and 'keep_background_music' in self.composition_checkboxes
        is_checked = has_bg_checkbox and self.composition_checkboxes['keep_background_music'].isChecked()
        
        print(f"📋 复选框容器存在: {has_checkboxes}")
        print(f"📋 背景音乐复选框存在: {has_bg_checkbox}")
        print(f"📋 背景音乐选项已勾选: {is_checked}")
        
        keep_background_music = (
            hasattr(self, 'composition_checkboxes') and
            'keep_background_music' in self.composition_checkboxes and
            self.composition_checkboxes['keep_background_music'].isChecked()
        )
        
        print(f"🎵 最终决定保留背景音乐: {keep_background_music}")
        return keep_background_music
    
    def validate_background_music_requirements(self):
        """验证保留背景音乐的要求"""
        try:
            print("🔍 验证背景音乐保留要求...")
            
            # 检查背景音乐文件是否存在
            background_music_path = self.get_background_music_path()
            if not background_music_path:
                return {
                    'valid': False,
                    'message': (
                        "未找到背景音乐文件。\n\n"
                        "请先执行人声分离操作，生成背景音乐文件后\n"
                        "再尝试保留背景音乐。"
                    )
                }

            # 验证背景音乐文件的有效性
            if not self.validate_background_music_file(background_music_path):
                return {
                    'valid': False,
                    'message': (
                        "背景音乐文件无效。\n\n"
                        "文件可能损坏、过小或为静音。\n"
                        "请重新执行人声分离操作。"
                    )
                }

            print("✅ 背景音乐保留条件检查通过")
            return {
                'valid': True,
                'message': "背景音乐保留条件满足"
            }

        except Exception as e:
            print(f"❌ 验证背景音乐要求失败: {e}")
            return {
                'valid': False,
                'message': f"验证过程出错: {str(e)}"
            }
    
    def validate_background_music_file(self, file_path):
        """验证背景音乐文件的有效性"""
        try:
            print(f"🔍 验证背景音乐文件: {file_path}")
            
            if not os.path.exists(file_path):
                print("❌ 文件不存在")
                return False

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            print(f"📊 文件大小: {file_size} bytes")
            
            if file_size < 1024:  # 小于1KB认为无效
                print(f"❌ 背景音乐文件太小: {file_size} bytes")
                return False

            # 尝试使用pydub验证音频文件
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(file_path)

                # 检查音频长度
                duration = len(audio)
                if duration < 1000:  # 小于1秒认为无效
                    print(f"❌ 背景音乐文件太短: {duration}ms")
                    return False

                # 检查音频是否为静音
                if audio.dBFS == float('-inf'):
                    print("❌ 背景音乐文件为静音")
                    return False

                print(f"✅ 背景音乐文件有效: 时长={duration/1000:.2f}s, 音量={audio.dBFS:.1f}dB")
                return True

            except ImportError:
                print("⚠️ 缺少pydub库，跳过音频内容验证")
                return True  # 如果没有pydub，只检查文件存在和大小
            except Exception as e:
                print(f"❌ 音频文件验证失败: {e}")
                return False

        except Exception as e:
            print(f"❌ 验证背景音乐文件失败: {e}")
            return False

class MockCheckbox:
    def __init__(self, checked=False):
        self._checked = checked
    
    def isChecked(self):
        return self._checked
    
    def setChecked(self, checked):
        self._checked = checked

def test_background_music_logic():
    """测试背景音乐逻辑"""
    print("🧪 测试UI背景音乐逻辑")
    print("=" * 50)
    
    # 创建模拟UI实例
    ui = MockFlipTalkUI()
    
    # 测试1: 检查背景音乐选项状态
    print("\n📋 测试1: 检查背景音乐选项状态")
    keep_bg = ui.check_background_music_option()
    
    if not keep_bg:
        print("❌ 背景音乐选项未勾选，这可能是问题所在！")
        return False
    
    # 测试2: 验证背景音乐要求
    print("\n📋 测试2: 验证背景音乐要求")
    validation_result = ui.validate_background_music_requirements()
    
    print(f"🔍 验证结果: {validation_result}")
    
    if not validation_result['valid']:
        print(f"❌ 背景音乐要求验证失败: {validation_result['message']}")
        return False
    
    print("✅ 所有检查通过，背景音乐应该能正常工作")
    return True

def main():
    """主函数"""
    print("🚀 UI背景音乐逻辑测试")
    print("=" * 60)
    
    success = test_background_music_logic()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ UI逻辑测试通过")
        print("\n💡 如果您的实际操作中仍然没有背景音乐，请:")
        print("1. 确保在UI中真的勾选了'保留背景音乐'选项")
        print("2. 查看控制台输出，寻找背景音乐相关的调试信息")
        print("3. 检查是否有错误或警告消息")
        print("4. 确认synthesis目录中是否生成了mixed_audio_with_background_*.wav文件")
    else:
        print("❌ UI逻辑测试失败")
        print("\n💡 这可能解释了为什么背景音乐没有出现在最终视频中")

if __name__ == "__main__":
    main()
