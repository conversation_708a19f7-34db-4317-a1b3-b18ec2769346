#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试背景音乐验证逻辑
"""

import os
import sys

class MockUI:
    """模拟UI类来测试验证逻辑"""
    
    def __init__(self):
        # 模拟参数面板
        self.parameter_panel = MockParameterPanel()
        # 模拟复选框
        self.composition_checkboxes = {
            'keep_background_music': MockCheckbox(True)
        }
    
    def check_voice_separation_enabled(self):
        """检查是否启用了人声分离功能"""
        try:
            print("🔍 检查人声分离是否启用...")
            
            # 检查参数设置面板中的人声分离选项
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'voice_separation_checkbox'):
                checkbox_result = self.parameter_panel.voice_separation_checkbox.isChecked()
                print(f"📋 参数面板人声分离复选框: {checkbox_result}")
                if checkbox_result:
                    return True

            # 如果没有找到复选框，检查是否有人声分离的输出文件
            print("📋 检查人声分离输出文件...")
            voice_separation_dir = self.get_voice_separation_output_dir()
            print(f"📂 人声分离目录: {voice_separation_dir}")
            
            if voice_separation_dir and os.path.exists(voice_separation_dir):
                # 检查目录中是否有分离后的文件
                files = os.listdir(voice_separation_dir)
                print(f"📁 目录中的文件: {files}")
                
                background_files = [f for f in files if 'background' in f.lower() or 'other' in f.lower()]
                print(f"🎵 背景音乐文件: {background_files}")
                
                file_check_result = len(background_files) > 0
                print(f"📋 基于文件的检查结果: {file_check_result}")
                return file_check_result

            print("❌ 人声分离目录不存在")
            return False

        except Exception as e:
            print(f"❌ 检查人声分离状态失败: {e}")
            return False
    
    def get_voice_separation_output_dir(self):
        """获取人声分离输出目录"""
        try:
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'output_path_edit'):
                output_dir = self.parameter_panel.output_path_edit.text().strip()
                if output_dir:
                    return os.path.join(output_dir, "voice_separation")

            # 默认输出目录
            return os.path.join("output", "voice_separation")

        except Exception as e:
            print(f"❌ 获取人声分离输出目录失败: {e}")
            return None
    
    def get_background_music_path(self):
        """获取背景音乐文件路径"""
        try:
            print("🔍 开始获取背景音乐文件路径...")
            voice_separation_dir = self.get_voice_separation_output_dir()
            print(f"📂 人声分离目录: {voice_separation_dir}")
            
            if not voice_separation_dir:
                print("❌ 人声分离目录路径为空")
                return None
                
            if not os.path.exists(voice_separation_dir):
                print(f"❌ 人声分离目录不存在: {voice_separation_dir}")
                return None

            # 查找背景音乐文件（可能的命名模式）
            possible_patterns = [
                "*background*.wav",
                "*background*.mp3",
                "*other*.wav",
                "*other*.mp3",
                "*instrumental*.wav",
                "*instrumental*.mp3"
            ]

            print(f"🔍 搜索背景音乐文件，模式: {possible_patterns}")
            
            import glob
            all_files = os.listdir(voice_separation_dir)
            print(f"📁 目录中的所有文件: {all_files}")
            
            for pattern in possible_patterns:
                search_path = os.path.join(voice_separation_dir, pattern)
                print(f"🔍 搜索模式: {search_path}")
                files = glob.glob(search_path)
                print(f"📋 匹配的文件: {files}")
                
                if files:
                    # 返回最新的文件
                    latest_file = max(files, key=os.path.getmtime)
                    print(f"🎵 找到背景音乐文件: {latest_file}")
                    return latest_file

            print("❌ 未找到背景音乐文件")
            return None

        except Exception as e:
            print(f"❌ 获取背景音乐文件路径失败: {e}")
            return None
    
    def validate_background_music_file(self, file_path):
        """验证背景音乐文件的有效性"""
        try:
            print(f"🔍 验证背景音乐文件: {file_path}")
            
            if not os.path.exists(file_path):
                print("❌ 文件不存在")
                return False

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            print(f"📊 文件大小: {file_size} bytes")
            
            if file_size < 1024:  # 小于1KB认为无效
                print(f"❌ 背景音乐文件太小: {file_size} bytes")
                return False

            # 尝试使用pydub验证音频文件
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(file_path)

                # 检查音频长度
                duration = len(audio)
                if duration < 1000:  # 小于1秒认为无效
                    print(f"❌ 背景音乐文件太短: {duration}ms")
                    return False

                # 检查音频是否为静音
                if audio.dBFS == float('-inf'):
                    print("❌ 背景音乐文件为静音")
                    return False

                print(f"✅ 背景音乐文件有效: 时长={duration/1000:.2f}s, 音量={audio.dBFS:.1f}dB")
                return True

            except ImportError:
                print("⚠️ 缺少pydub库，跳过音频内容验证")
                return True  # 如果没有pydub，只检查文件存在和大小
            except Exception as e:
                print(f"❌ 音频文件验证失败: {e}")
                return False

        except Exception as e:
            print(f"❌ 验证背景音乐文件失败: {e}")
            return False
    
    def validate_background_music_requirements(self):
        """验证保留背景音乐的要求"""
        try:
            print("🔍 验证背景音乐保留要求...")
            
            # 检查是否启用了人声分离
            voice_separation_enabled = self.check_voice_separation_enabled()
            print(f"📋 人声分离启用状态: {voice_separation_enabled}")
            
            if not voice_separation_enabled:
                return {
                    'valid': False,
                    'message': (
                        "要保留背景音乐，需要先启用人声分离功能。\n\n"
                        "请在首页选项设置中勾选\"人声分离\"，\n"
                        "并完成音频的人声分离处理。"
                    )
                }

            # 检查背景音乐文件是否存在
            background_music_path = self.get_background_music_path()
            if not background_music_path or not os.path.exists(background_music_path):
                return {
                    'valid': False,
                    'message': (
                        "未找到背景音乐文件。\n\n"
                        "请先执行人声分离操作，生成背景音乐文件后\n"
                        "再尝试保留背景音乐。"
                    )
                }

            # 验证背景音乐文件的有效性
            if not self.validate_background_music_file(background_music_path):
                return {
                    'valid': False,
                    'message': (
                        "背景音乐文件损坏或无效。\n\n"
                        "请重新执行人声分离操作。"
                    )
                }

            print("✅ 背景音乐保留条件检查通过")
            return {
                'valid': True,
                'message': "背景音乐保留条件满足"
            }

        except Exception as e:
            print(f"❌ 验证背景音乐要求失败: {e}")
            return {
                'valid': False,
                'message': f"验证过程中出现错误: {str(e)}"
            }

class MockParameterPanel:
    """模拟参数面板"""
    def __init__(self):
        self.voice_separation_checkbox = MockCheckbox(True)  # 模拟已勾选
        self.output_path_edit = MockLineEdit("output")

class MockLineEdit:
    """模拟文本输入框"""
    def __init__(self, text=""):
        self._text = text
    
    def text(self):
        return self._text

class MockCheckbox:
    """模拟复选框"""
    def __init__(self, checked=False):
        self._checked = checked
    
    def isChecked(self):
        return self._checked

def test_validation_logic():
    """测试验证逻辑"""
    print("🧪 测试背景音乐验证逻辑")
    print("=" * 50)
    
    ui = MockUI()
    
    # 测试1: 检查人声分离启用状态
    print("\n📋 测试1: 检查人声分离启用状态")
    voice_sep_enabled = ui.check_voice_separation_enabled()
    print(f"🔍 结果: {voice_sep_enabled}")
    
    # 测试2: 获取背景音乐路径
    print("\n📋 测试2: 获取背景音乐路径")
    bg_path = ui.get_background_music_path()
    print(f"🔍 结果: {bg_path}")
    
    # 测试3: 验证背景音乐文件
    if bg_path:
        print("\n📋 测试3: 验证背景音乐文件")
        file_valid = ui.validate_background_music_file(bg_path)
        print(f"🔍 结果: {file_valid}")
    
    # 测试4: 完整验证流程
    print("\n📋 测试4: 完整验证流程")
    validation_result = ui.validate_background_music_requirements()
    print(f"🔍 结果: {validation_result}")
    
    return validation_result

def main():
    """主函数"""
    print("🚀 背景音乐验证逻辑测试")
    print("=" * 60)
    
    result = test_validation_logic()
    
    print("\n" + "=" * 60)
    print("🔍 测试结论:")
    
    if result['valid']:
        print("✅ 验证逻辑正常，背景音乐要求满足")
        print("💡 问题可能在于:")
        print("   1. UI中的复选框状态检查有问题")
        print("   2. 背景音乐混合函数调用条件不满足")
        print("   3. 程序流程中的其他逻辑错误")
    else:
        print("❌ 验证逻辑失败")
        print(f"❌ 失败原因: {result['message']}")
        print("💡 这可能解释了为什么背景音乐没有被混合")

if __name__ == "__main__":
    main()
