#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟完整的视频合成过程
找出背景音乐混合步骤被跳过的原因
"""

import os
import sys
import datetime

class MockFlipTalkUI:
    """模拟FlipTalk UI类"""
    
    def __init__(self):
        # 模拟复选框状态
        self.composition_checkboxes = {
            'keep_background_music': MockCheckbox(True)  # 模拟用户勾选状态
        }
        
        # 模拟参数面板
        self.parameter_panel = MockParameterPanel()
        
        # 模拟当前视频路径
        self.current_video_path = "test_video.mp4"
    
    def simulate_video_synthesis(self):
        """模拟视频合成过程"""
        print("🎬 开始模拟视频合成过程...")
        
        # 模拟配音合并完成
        combined_audio_path = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\combined_voiceover.wav"
        synthesis_dir = r"J:\MyAi\03 FlipTalk Ai\output\synthesis"
        
        print(f"📂 配音文件: {combined_audio_path}")
        print(f"📂 合成目录: {synthesis_dir}")
        
        # 检查是否需要保留背景音乐 - 这是关键步骤！
        print("\n🔍 检查背景音乐保留选项...")
        
        has_checkboxes = hasattr(self, 'composition_checkboxes')
        has_bg_checkbox = has_checkboxes and 'keep_background_music' in self.composition_checkboxes
        is_checked = has_bg_checkbox and self.composition_checkboxes['keep_background_music'].isChecked()
        
        print(f"📋 复选框容器存在: {has_checkboxes}")
        print(f"📋 背景音乐复选框存在: {has_bg_checkbox}")
        print(f"📋 背景音乐选项已勾选: {is_checked}")
        
        keep_background_music = (
            hasattr(self, 'composition_checkboxes') and
            'keep_background_music' in self.composition_checkboxes and
            self.composition_checkboxes['keep_background_music'].isChecked()
        )
        
        print(f"🎵 最终决定保留背景音乐: {keep_background_music}")

        final_audio_path = combined_audio_path

        if keep_background_music:
            print("\n🎵 执行背景音乐混合...")
            # 第二步：混合背景音乐
            print("正在混合背景音乐...")
            final_audio_path = self.mix_background_music(combined_audio_path, synthesis_dir)

            if not final_audio_path:
                print("⚠️ 背景音乐混合失败，使用纯配音音频")
                final_audio_path = combined_audio_path
            else:
                print(f"✅ 背景音乐混合成功: {final_audio_path}")
        else:
            print("\n❌ 跳过背景音乐混合步骤")
            print("💡 原因: 背景音乐保留选项未勾选或验证失败")

        # 第三步：替换视频音频
        print(f"\n🎬 使用音频文件进行视频合成: {final_audio_path}")
        final_video_path = self.replace_video_audio(final_audio_path, synthesis_dir)

        if final_video_path:
            print(f"✅ 视频合成完成: {final_video_path}")
        else:
            print("❌ 视频合成失败")
        
        return final_video_path
    
    def mix_background_music(self, voiceover_audio_path, output_dir):
        """将背景音乐与配音混合"""
        try:
            from pydub import AudioSegment
            import datetime

            print("🎵 开始混合背景音乐...")
            print(f"📂 配音文件路径: {voiceover_audio_path}")
            print(f"📂 输出目录: {output_dir}")

            # 获取背景音乐文件路径
            print("🔍 正在获取背景音乐文件路径...")
            background_music_path = self.get_background_music_path()
            
            print(f"🎵 获取到的背景音乐路径: {background_music_path}")
            
            if not background_music_path:
                print("❌ 背景音乐路径为空")
                return None
                
            if not os.path.exists(background_music_path):
                print(f"❌ 背景音乐文件不存在: {background_music_path}")
                return None

            print(f"🎵 背景音乐文件: {background_music_path}")
            print(f"🎤 配音文件: {voiceover_audio_path}")

            # 加载音频文件
            try:
                voiceover_audio = AudioSegment.from_wav(voiceover_audio_path)
                background_music = AudioSegment.from_file(background_music_path)

                print(f"📊 配音音频: 长度={len(voiceover_audio)/1000:.2f}s, 音量={voiceover_audio.dBFS:.1f}dB")
                print(f"📊 背景音乐: 长度={len(background_music)/1000:.2f}s, 音量={background_music.dBFS:.1f}dB")

            except Exception as e:
                print(f"❌ 加载音频文件失败: {e}")
                return None

            # 调整背景音乐长度以匹配配音长度
            voiceover_duration = len(voiceover_audio)
            background_duration = len(background_music)

            if background_duration > voiceover_duration:
                # 背景音乐较长，截取前面部分
                background_music = background_music[:voiceover_duration]
                print(f"🔧 背景音乐已截取到 {voiceover_duration/1000:.2f}s")
            elif background_duration < voiceover_duration:
                # 背景音乐较短，循环播放
                repeat_times = int(voiceover_duration / background_duration) + 1
                background_music = background_music * repeat_times
                background_music = background_music[:voiceover_duration]
                print(f"🔧 背景音乐已循环 {repeat_times} 次并截取到 {voiceover_duration/1000:.2f}s")

            # 智能调整背景音乐音量
            bg_current_db = background_music.dBFS
            voiceover_db = voiceover_audio.dBFS

            print(f"📊 配音音量: {voiceover_db:.1f}dB, 背景音乐音量: {bg_current_db:.1f}dB")

            # 计算目标背景音乐音量（相对于配音音量降低7dB）
            target_bg_db = voiceover_db - 7
            gain_adjustment = target_bg_db - bg_current_db
            
            # 应用增益调整
            background_music = background_music + gain_adjustment
            adjusted_bg_db = background_music.dBFS
            print(f"🔧 背景音乐音量调整: {gain_adjustment:+.1f}dB (从 {bg_current_db:.1f}dB 到 {adjusted_bg_db:.1f}dB)")

            # 混合音频
            print("🎵 正在混合音频...")
            mixed_audio = voiceover_audio.overlay(background_music)

            # 检查混合后的音频质量
            mixed_db = mixed_audio.dBFS
            print(f"📊 混合后音频: 长度={len(mixed_audio)/1000:.2f}s, 音量={mixed_db:.1f}dB")

            # 智能音量调整和归一化
            if mixed_db > -3:  # 避免削波
                volume_adjustment = -3 - mixed_db
                mixed_audio = mixed_audio + volume_adjustment
                print(f"🔧 混合音频音量已调整 {volume_adjustment:.1f}dB 以避免削波")

            # 如果混合后音频过小，进行适度增益
            elif mixed_db < -18:  # 如果音量过小
                volume_boost = -15 - mixed_db  # 提升到-15dB
                mixed_audio = mixed_audio + volume_boost
                print(f"🔧 混合音频音量已提升 {volume_boost:.1f}dB 以改善听感")

            # 导出混合后的音频
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            mixed_filename = f"mixed_audio_with_background_{timestamp}.wav"
            mixed_audio_path = os.path.join(output_dir, mixed_filename)

            mixed_audio.export(mixed_audio_path, format="wav")
            
            final_db = mixed_audio.dBFS
            print(f"✅ 背景音乐混合完成: {mixed_filename}")
            print(f"🎵 最终音频质量: 音量={final_db:.1f}dB, 时长={len(mixed_audio)/1000:.2f}s")

            return mixed_audio_path

        except ImportError:
            print("❌ 缺少 pydub 库，无法混合背景音乐")
            return None
        except Exception as e:
            print(f"❌ 混合背景音乐失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_background_music_path(self):
        """获取背景音乐文件路径"""
        # 直接返回已知的背景音乐文件路径
        return r"J:\MyAi\03 FlipTalk Ai\output\voice_separation\Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav"
    
    def replace_video_audio(self, audio_path, output_dir):
        """模拟视频音频替换"""
        print(f"🎬 模拟视频音频替换...")
        print(f"📁 使用音频文件: {audio_path}")
        
        # 检查音频文件是否存在
        if not os.path.exists(audio_path):
            print(f"❌ 音频文件不存在: {audio_path}")
            return None
        
        # 模拟生成输出文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"simulated_translated_{timestamp}.mp4"
        output_video_path = os.path.join(output_dir, output_filename)
        
        print(f"📁 模拟输出视频: {output_video_path}")
        
        # 检查使用的音频文件类型
        if "mixed_audio_with_background" in audio_path:
            print("✅ 使用了包含背景音乐的混合音频")
        else:
            print("❌ 使用了纯配音音频，没有背景音乐")
        
        return output_video_path

class MockParameterPanel:
    """模拟参数面板"""
    def __init__(self):
        self.voice_separation_checkbox = MockCheckbox(True)
        self.output_path_edit = MockLineEdit("output")

class MockLineEdit:
    """模拟文本输入框"""
    def __init__(self, text=""):
        self._text = text
    
    def text(self):
        return self._text

class MockCheckbox:
    """模拟复选框"""
    def __init__(self, checked=False):
        self._checked = checked
    
    def isChecked(self):
        return self._checked
    
    def setChecked(self, checked):
        self._checked = checked

def main():
    """主函数"""
    print("🚀 模拟视频合成过程")
    print("=" * 60)
    
    ui = MockFlipTalkUI()
    result = ui.simulate_video_synthesis()
    
    print("\n" + "=" * 60)
    print("🔍 模拟结果:")
    
    if result:
        print(f"✅ 模拟成功: {result}")
        
        # 检查实际的synthesis目录
        synthesis_dir = r"J:\MyAi\03 FlipTalk Ai\output\synthesis"
        files = os.listdir(synthesis_dir)
        
        mixed_files = [f for f in files if "mixed_audio_with_background" in f]
        if mixed_files:
            print(f"✅ 找到混合音频文件: {mixed_files}")
        else:
            print("❌ 没有找到混合音频文件")
            print("💡 这说明在实际操作中，背景音乐混合步骤被跳过了")
    else:
        print("❌ 模拟失败")

if __name__ == "__main__":
    main()
