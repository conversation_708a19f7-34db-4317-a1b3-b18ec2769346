#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试背景音乐混合修复
"""

import os
import sys
from pydub import AudioSegment

def test_background_music_mixing():
    """测试背景音乐混合功能"""
    print("🧪 测试背景音乐混合修复...")
    
    # 文件路径
    background_file = "output/voice_separation/Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav"
    voiceover_file = "output/synthesis/combined_voiceover.wav"
    output_file = "output/synthesis/test_mixed_audio.wav"
    
    # 检查文件存在
    if not os.path.exists(background_file):
        print(f"❌ 背景音乐文件不存在: {background_file}")
        return False
    
    if not os.path.exists(voiceover_file):
        print(f"❌ 配音文件不存在: {voiceover_file}")
        return False
    
    try:
        # 加载音频文件
        print("📂 加载音频文件...")
        voiceover_audio = AudioSegment.from_wav(voiceover_file)
        background_music = AudioSegment.from_file(background_file)
        
        print(f"📊 配音音频: 长度={len(voiceover_audio)/1000:.2f}s, 音量={voiceover_audio.dBFS:.1f}dB")
        print(f"📊 背景音乐: 长度={len(background_music)/1000:.2f}s, 音量={background_music.dBFS:.1f}dB")
        
        # 调整背景音乐长度
        voiceover_duration = len(voiceover_audio)
        background_duration = len(background_music)
        
        if background_duration > voiceover_duration:
            background_music = background_music[:voiceover_duration]
            print(f"🔧 背景音乐已截取到 {voiceover_duration/1000:.2f}s")
        elif background_duration < voiceover_duration:
            repeat_times = int(voiceover_duration / background_duration) + 1
            background_music = background_music * repeat_times
            background_music = background_music[:voiceover_duration]
            print(f"🔧 背景音乐已循环 {repeat_times} 次")
        
        # 应用新的音量调整逻辑
        bg_current_db = background_music.dBFS
        voiceover_db = voiceover_audio.dBFS
        
        print(f"📊 原始音量 - 配音: {voiceover_db:.1f}dB, 背景音乐: {bg_current_db:.1f}dB")
        
        # 计算目标背景音乐音量（相对于配音音量降低7dB）
        target_bg_db = voiceover_db - 7
        gain_adjustment = target_bg_db - bg_current_db
        
        # 应用增益调整
        background_music = background_music + gain_adjustment
        adjusted_bg_db = background_music.dBFS
        
        print(f"🔧 音量调整: {gain_adjustment:+.1f}dB")
        print(f"📊 调整后音量 - 配音: {voiceover_db:.1f}dB, 背景音乐: {adjusted_bg_db:.1f}dB")
        print(f"📊 音量差异: {voiceover_db - adjusted_bg_db:.1f}dB")
        
        # 混合音频
        print("🎵 混合音频...")
        mixed_audio = voiceover_audio.overlay(background_music)
        
        # 检查混合结果
        mixed_db = mixed_audio.dBFS
        print(f"📊 混合后音频: 长度={len(mixed_audio)/1000:.2f}s, 音量={mixed_db:.1f}dB")
        
        # 防止削波
        if mixed_db > -3:
            volume_adjustment = -3 - mixed_db
            mixed_audio = mixed_audio + volume_adjustment
            print(f"🔧 防削波调整: {volume_adjustment:.1f}dB")
        
        # 导出测试文件
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        mixed_audio.export(output_file, format="wav")
        
        final_db = mixed_audio.dBFS
        print(f"✅ 测试混合完成!")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 最终音频: 音量={final_db:.1f}dB, 时长={len(mixed_audio)/1000:.2f}s")
        
        # 验证文件
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"📊 文件大小: {file_size / (1024*1024):.2f} MB")
            
            # 重新加载验证
            test_audio = AudioSegment.from_wav(output_file)
            print(f"📊 验证加载: 音量={test_audio.dBFS:.1f}dB, 时长={len(test_audio)/1000:.2f}s")
            
            return True
        else:
            print("❌ 输出文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_volume_levels():
    """分析音量水平"""
    print("\n📊 音量水平分析:")
    
    files = {
        "背景音乐": "output/voice_separation/Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav",
        "配音": "output/synthesis/combined_voiceover.wav"
    }
    
    if os.path.exists("output/synthesis/test_mixed_audio.wav"):
        files["混合音频"] = "output/synthesis/test_mixed_audio.wav"
    
    for name, path in files.items():
        if os.path.exists(path):
            try:
                audio = AudioSegment.from_file(path)
                print(f"🎵 {name}: {audio.dBFS:.1f}dB ({len(audio)/1000:.1f}s)")
            except Exception as e:
                print(f"❌ {name}: 无法分析 - {e}")
        else:
            print(f"❌ {name}: 文件不存在")

def main():
    """主函数"""
    print("🚀 背景音乐混合修复测试")
    print("=" * 50)
    
    # 分析当前音量水平
    analyze_volume_levels()
    
    # 测试混合功能
    success = test_background_music_mixing()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试成功! 背景音乐混合功能已修复")
        print("\n💡 使用建议:")
        print("1. 在视频合成时勾选'保留背景音乐'选项")
        print("2. 背景音乐现在会自动调整到合适的音量水平")
        print("3. 混合后的音频会包含清晰的配音和适度的背景音乐")
    else:
        print("❌ 测试失败，请检查错误信息")
    
    # 再次分析音量水平
    print("\n📊 修复后音量水平:")
    analyze_volume_levels()

if __name__ == "__main__":
    main()
