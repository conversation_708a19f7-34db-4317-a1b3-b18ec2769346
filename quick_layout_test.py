#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试新的日志布局
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 快速测试新日志布局")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.log_area import LogArea
        from core.logger import get_logger, log_info, log_warning, log_error, log_debug
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("📱 创建LogArea...")
        log_area = LogArea()
        
        # 添加测试日志
        logger = get_logger()
        log_info("测试", "信息日志测试")
        log_warning("测试", "警告日志测试")
        log_error("测试", "错误日志测试")
        log_debug("测试", "调试日志测试")
        log_info("测试", "更多信息日志")
        log_error("测试", "更多错误日志")
        
        print("📝 已添加6条测试日志")
        
        # 显示LogArea
        log_area.show()
        print("🖥️ LogArea已显示")
        
        print("\n✅ 新布局特色:")
        print("• 顶部统计标签显示各级别日志数量")
        print("• 右侧过滤面板已移除")
        print("• 日志显示区域占据全部空间")
        print("• 点击'过滤'按钮可打开过滤设置")
        
        print("\n⌨️ 按 Ctrl+C 退出")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
