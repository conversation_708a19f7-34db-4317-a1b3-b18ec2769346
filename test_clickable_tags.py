#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可点击的统计标签功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 测试可点击统计标签功能")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.log_area import LogArea
        from core.logger import get_logger, log_info, log_warning, log_error, log_debug
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("📱 创建LogArea...")
        log_area = LogArea()
        
        # 添加大量测试日志
        logger = get_logger()
        
        # 添加不同数量的各级别日志
        for i in range(8):
            log_info("测试", f"信息日志 {i+1}")
        
        for i in range(4):
            log_warning("测试", f"警告日志 {i+1}")
        
        for i in range(3):
            log_error("测试", f"错误日志 {i+1}")
        
        for i in range(2):
            log_debug("测试", f"调试日志 {i+1}")
        
        print("📝 已添加测试日志:")
        print("   - 信息: 8条")
        print("   - 警告: 4条")
        print("   - 错误: 3条")
        print("   - 调试: 2条")
        print("   - 总计: 17条")
        
        # 显示LogArea
        log_area.show()
        print("🖥️ LogArea已显示")
        
        print("\n✅ 新功能特色:")
        print("1. 📊 统计标签合并为一行显示")
        print("2. 🖱️ 点击标签可切换该级别的过滤状态")
        print("3. 🎨 激活/禁用状态有不同的视觉效果")
        print("4. 🔄 '全部'按钮可重置所有过滤器")
        print("5. ⚡ 实时更新统计数据")
        
        print("\n🎯 使用说明:")
        print("• 点击'错误'标签 - 切换错误日志显示")
        print("• 点击'警告'标签 - 切换警告日志显示")
        print("• 点击'信息'标签 - 切换信息日志显示")
        print("• 点击'调试'标签 - 切换调试日志显示")
        print("• 点击'总计'标签 - 显示所有日志")
        print("• 点击'全部'按钮 - 重置所有过滤器")
        
        print("\n🎨 视觉效果:")
        print("• 激活状态: 彩色背景，白色文字")
        print("• 禁用状态: 灰色背景，灰色文字，虚线边框")
        print("• 悬停效果: 颜色变亮，轻微缩放")
        print("• 点击反馈: 颜色变暗")
        
        print("\n⌨️ 按 Ctrl+C 退出测试")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
