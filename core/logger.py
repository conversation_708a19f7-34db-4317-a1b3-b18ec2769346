#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI 日志管理系统
提供分级日志记录、颜色显示、过滤和导出功能
"""

import os
import sys
import datetime
from typing import List, Dict, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from PySide6.QtCore import QObject, Signal, QMutex, QMutexLocker


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3


@dataclass
class LogEntry:
    """日志条目数据类"""
    timestamp: datetime.datetime
    level: LogLevel
    module: str
    message: str
    
    def __str__(self):
        """格式化日志条目为字符串"""
        timestamp_str = self.timestamp.strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
        level_str = self.level.name.ljust(7)  # 对齐级别名称
        return f"[{timestamp_str}] [{level_str}] [{self.module}] {self.message}"
    
    def to_html(self, color_map: Dict[LogLevel, str]) -> str:
        """转换为HTML格式，支持颜色显示"""
        timestamp_str = self.timestamp.strftime("%H:%M:%S.%f")[:-3]
        level_str = self.level.name.ljust(7)
        color = color_map.get(self.level, "#FFFFFF")
        
        return (f'<span style="color: {color};">'
                f'[{timestamp_str}] [{level_str}] [{self.module}] {self.message}'
                f'</span>')


class LogManager(QObject):
    """日志管理器 - 线程安全的单例模式"""
    
    # 信号定义
    log_added = Signal(LogEntry)  # 新日志添加信号
    logs_cleared = Signal()       # 日志清除信号
    
    _instance = None
    _mutex = QMutex()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        super().__init__()
        self._initialized = True
        
        # 日志存储
        self._logs: List[LogEntry] = []
        self._max_logs = 1000  # 最大日志条数
        
        # 日志级别颜色映射
        self._color_map = {
            LogLevel.ERROR: "#FF6B6B",    # 红色
            LogLevel.WARNING: "#FFA726",  # 橙色
            LogLevel.INFO: "#4A9EFF",     # 蓝色
            LogLevel.DEBUG: "#CCCCCC"     # 灰色
        }
        
        # 过滤设置
        self._enabled_levels = {level: True for level in LogLevel}
        
        # 文件输出设置
        self._file_output_enabled = False
        self._log_file_path = None
        
        # 线程安全锁
        self._log_mutex = QMutex()
        
        print("日志管理器初始化完成")
    
    @classmethod
    def get_instance(cls) -> 'LogManager':
        """获取日志管理器实例"""
        return cls()
    
    def set_max_logs(self, max_logs: int):
        """设置最大日志条数"""
        with QMutexLocker(self._log_mutex):
            self._max_logs = max_logs
            self._trim_logs()
    
    def set_level_enabled(self, level: LogLevel, enabled: bool):
        """设置日志级别是否启用"""
        with QMutexLocker(self._log_mutex):
            self._enabled_levels[level] = enabled
    
    def is_level_enabled(self, level: LogLevel) -> bool:
        """检查日志级别是否启用"""
        return self._enabled_levels.get(level, True)
    
    def get_color_map(self) -> Dict[LogLevel, str]:
        """获取颜色映射"""
        return self._color_map.copy()
    
    def set_file_output(self, enabled: bool, file_path: Optional[str] = None):
        """设置文件输出"""
        with QMutexLocker(self._log_mutex):
            self._file_output_enabled = enabled
            if file_path:
                self._log_file_path = file_path
                # 确保目录存在
                dir_path = os.path.dirname(file_path)
                if dir_path:
                    os.makedirs(dir_path, exist_ok=True)
    
    def add_log(self, level: LogLevel, module: str, message: str):
        """添加日志条目"""
        # 检查级别是否启用
        if not self.is_level_enabled(level):
            return
        
        # 创建日志条目
        entry = LogEntry(
            timestamp=datetime.datetime.now(),
            level=level,
            module=module,
            message=message
        )
        
        with QMutexLocker(self._log_mutex):
            # 添加到内存
            self._logs.append(entry)
            
            # 修剪日志
            self._trim_logs()
            
            # 写入文件
            if self._file_output_enabled and self._log_file_path:
                self._write_to_file(entry)
        
        # 发射信号
        self.log_added.emit(entry)
    
    def _trim_logs(self):
        """修剪日志，保持在最大条数内"""
        if len(self._logs) > self._max_logs:
            # 删除最旧的日志
            excess = len(self._logs) - self._max_logs
            self._logs = self._logs[excess:]
    
    def _write_to_file(self, entry: LogEntry):
        """写入日志到文件"""
        try:
            with open(self._log_file_path, 'a', encoding='utf-8') as f:
                f.write(str(entry) + '\n')
        except Exception as e:
            print(f"写入日志文件失败: {e}")
    
    def get_logs(self, level_filter: Optional[List[LogLevel]] = None) -> List[LogEntry]:
        """获取日志列表，支持级别过滤"""
        with QMutexLocker(self._log_mutex):
            if level_filter is None:
                return self._logs.copy()
            else:
                return [log for log in self._logs if log.level in level_filter]
    
    def clear_logs(self):
        """清除所有日志"""
        with QMutexLocker(self._log_mutex):
            self._logs.clear()
        
        self.logs_cleared.emit()
    
    def export_logs(self, file_path: str, level_filter: Optional[List[LogLevel]] = None) -> bool:
        """导出日志到文件"""
        try:
            logs = self.get_logs(level_filter)
            
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path:  # 只有当目录路径不为空时才创建
                os.makedirs(dir_path, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"FlipTalk AI 运行日志\n")
                f.write(f"导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"日志条数: {len(logs)}\n")
                f.write("=" * 80 + "\n\n")
                
                for log in logs:
                    f.write(str(log) + '\n')
            
            return True
            
        except Exception as e:
            print(f"导出日志失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, int]:
        """获取日志统计信息"""
        with QMutexLocker(self._log_mutex):
            stats = {level.name: 0 for level in LogLevel}
            for log in self._logs:
                stats[log.level.name] += 1
            stats['TOTAL'] = len(self._logs)
            return stats


# 全局日志管理器实例
_logger_instance = None


def get_logger() -> LogManager:
    """获取全局日志管理器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = LogManager()
    return _logger_instance


# 便捷的日志记录函数
def log_debug(module: str, message: str):
    """记录调试日志"""
    get_logger().add_log(LogLevel.DEBUG, module, message)


def log_info(module: str, message: str):
    """记录信息日志"""
    get_logger().add_log(LogLevel.INFO, module, message)


def log_warning(module: str, message: str):
    """记录警告日志"""
    get_logger().add_log(LogLevel.WARNING, module, message)


def log_error(module: str, message: str):
    """记录错误日志"""
    get_logger().add_log(LogLevel.ERROR, module, message)


# 模块级别的日志记录器类
class ModuleLogger:
    """模块级别的日志记录器"""
    
    def __init__(self, module_name: str):
        self.module_name = module_name
        self._logger = get_logger()
    
    def debug(self, message: str):
        """记录调试日志"""
        self._logger.add_log(LogLevel.DEBUG, self.module_name, message)
    
    def info(self, message: str):
        """记录信息日志"""
        self._logger.add_log(LogLevel.INFO, self.module_name, message)
    
    def warning(self, message: str):
        """记录警告日志"""
        self._logger.add_log(LogLevel.WARNING, self.module_name, message)
    
    def error(self, message: str):
        """记录错误日志"""
        self._logger.add_log(LogLevel.ERROR, self.module_name, message)


def create_module_logger(module_name: str) -> ModuleLogger:
    """创建模块日志记录器"""
    return ModuleLogger(module_name)
