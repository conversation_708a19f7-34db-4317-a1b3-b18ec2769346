#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行日志页面显示
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_log_area_standalone():
    """测试LogArea独立显示"""
    print("🧪 测试LogArea独立显示...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.log_area import LogArea
        from core.logger import get_logger, log_info, log_warning, log_error
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建日志区域
        log_area = LogArea()
        log_area.show()
        
        # 添加一些测试日志
        logger = get_logger()
        log_info("TestModule", "LogArea独立显示测试")
        log_warning("TestModule", "这是一个警告信息")
        log_error("TestModule", "这是一个错误信息")
        
        print("✅ LogArea独立显示成功")
        print("💡 窗口应该已经显示，包含测试日志")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
        return True
        
    except Exception as e:
        print(f"❌ LogArea独立显示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_log_page():
    """测试主窗口中的日志页面"""
    print("\n🏠 测试主窗口中的日志页面...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import FlipTalkMainWindow
        from core.logger import get_logger, log_info
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = FlipTalkMainWindow()
        
        # 检查LogArea是否正确创建
        if hasattr(main_window, 'log_area'):
            print("✅ 主窗口包含log_area属性")
            
            # 检查LogArea类型
            log_area_type = type(main_window.log_area).__name__
            print(f"📋 log_area类型: {log_area_type}")
            
            # 检查LogArea大小
            size = main_window.log_area.size()
            print(f"📏 log_area大小: {size.width()} x {size.height()}")
            
            # 添加测试日志
            logger = get_logger()
            log_info("MainWindowTest", "主窗口日志页面测试")
            
            # 手动切换到日志页面
            print("🔄 切换到日志页面...")
            main_window.on_page_changed("运行日志")
            
            # 检查日志区域是否可见
            if main_window.log_area.isVisible():
                print("✅ 日志区域已显示")
            else:
                print("❌ 日志区域未显示")
                return False
            
        else:
            print("❌ 主窗口缺少log_area属性")
            return False
        
        print("✅ 主窗口日志页面测试完成")
        
        # 显示主窗口
        main_window.show()
        
        print("💡 主窗口已显示，请点击'运行日志'按钮查看效果")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口日志页面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_area_import():
    """测试LogArea导入"""
    print("\n📦 测试LogArea导入...")
    
    try:
        # 测试直接导入
        from ui.widgets.log_area import LogArea
        print("✅ 直接导入ui.widgets.log_area.LogArea成功")
        
        # 测试相对导入（模拟主窗口的导入方式）
        try:
            import ui.widgets.log_area as log_area_module
            LogAreaClass = getattr(log_area_module, 'LogArea')
            print("✅ 模块导入ui.widgets.log_area成功")
        except Exception as e:
            print(f"❌ 模块导入失败: {e}")
            return False
        
        # 测试创建实例
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        log_area = LogArea()
        print(f"✅ LogArea实例创建成功，类型: {type(log_area)}")
        
        # 检查关键属性
        if hasattr(log_area, '_logger'):
            print("✅ LogArea包含_logger属性")
        else:
            print("❌ LogArea缺少_logger属性")
        
        if hasattr(log_area, 'log_display'):
            print("✅ LogArea包含log_display属性")
        else:
            print("❌ LogArea缺少log_display属性")
        
        if hasattr(log_area, 'filter_widget'):
            print("✅ LogArea包含filter_widget属性")
        else:
            print("❌ LogArea缺少filter_widget属性")
        
        return True
        
    except Exception as e:
        print(f"❌ LogArea导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_area_size_and_style():
    """测试LogArea大小和样式"""
    print("\n📐 测试LogArea大小和样式...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.log_area import LogArea
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建LogArea
        log_area = LogArea()
        
        # 检查大小
        size = log_area.size()
        print(f"📏 LogArea大小: {size.width()} x {size.height()}")
        
        # 检查固定大小
        if size.width() == 1359 and size.height() == 776:
            print("✅ LogArea大小正确 (1359 x 776)")
        else:
            print(f"⚠️ LogArea大小可能不正确，期望 1359x776，实际 {size.width()}x{size.height()}")
        
        # 检查样式表
        style_sheet = log_area.styleSheet()
        if style_sheet:
            print("✅ LogArea包含样式表")
            if "#1E1E1E" in style_sheet:
                print("✅ 样式表包含深色主题颜色")
            else:
                print("⚠️ 样式表可能缺少深色主题颜色")
        else:
            print("⚠️ LogArea缺少样式表")
        
        # 检查子组件
        children = log_area.findChildren(object)
        print(f"📦 LogArea子组件数量: {len(children)}")
        
        return True
        
    except Exception as e:
        print(f"❌ LogArea大小和样式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI 运行日志页面显示测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("LogArea导入", test_log_area_import),
        ("LogArea大小和样式", test_log_area_size_and_style),
        ("LogArea独立显示", test_log_area_standalone),
        ("主窗口日志页面", test_main_window_log_page)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！")
        print("\n💡 如果运行日志页面仍然不显示，可能的原因:")
        print("1. 主窗口的布局问题")
        print("2. 动画效果干扰")
        print("3. 样式表覆盖")
        print("4. 组件层级问题")
    else:
        print(f"\n❌ 有 {total_tests - passed_tests} 个测试失败")
        print("\n🔧 建议检查:")
        print("1. LogArea类的导入和创建")
        print("2. 主窗口的布局管理")
        print("3. 页面切换逻辑")
        print("4. 组件的显示状态")

if __name__ == "__main__":
    main()
