#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的日志布局（移除右侧面板，统计信息标签化）
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 FlipTalk AI 新日志布局测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import FlipTalkMainWindow
        from core.logger import get_logger, log_info, log_warning, log_error, log_debug
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("📱 创建主窗口...")
        main_window = FlipTalkMainWindow()
        
        # 添加大量测试日志来验证统计功能
        logger = get_logger()
        
        # 添加不同级别的日志
        log_info("系统", "FlipTalk AI 系统启动完成")
        log_info("GPU", "检测到 NVIDIA GeForce RTX 3080 Ti")
        log_warning("内存", "GPU内存使用率较高: 85%")
        log_info("插件", "加载音频提取插件成功")
        log_info("插件", "加载人声分离插件成功")
        log_error("网络", "网络连接超时，请检查网络设置")
        log_debug("缓存", "清理临时文件缓存")
        log_info("TTS", "Edge TTS 引擎初始化完成")
        log_warning("模型", "模型文件较大，加载可能需要时间")
        log_info("音频", "音频处理完成: sample_rate=44100Hz")
        log_error("文件", "无法读取视频文件: 文件格式不支持")
        log_info("导出", "字幕导出成功: output.srt")
        log_debug("性能", "处理耗时: 2.34秒")
        log_warning("磁盘", "磁盘空间不足，建议清理临时文件")
        log_info("UI", "用户界面初始化完成")
        log_error("插件", "插件加载失败: 依赖缺失")
        log_warning("配置", "配置文件格式过旧，建议更新")
        log_debug("调试", "变量值检查: status=ready")
        log_info("完成", "所有初始化步骤已完成")
        log_error("严重", "系统遇到严重错误")
        
        print("📝 已添加20条测试日志")
        print("   - 信息日志: 9条")
        print("   - 警告日志: 4条") 
        print("   - 错误日志: 4条")
        print("   - 调试日志: 3条")
        
        # 显示主窗口
        main_window.show()
        print("🖥️ 主窗口已显示")
        
        # 自动切换到日志页面
        print("🔄 自动切换到运行日志页面...")
        main_window.on_page_changed("运行日志")
        
        print("\n✅ 新日志布局测试完成！")
        print("\n🎨 新布局特色:")
        print("1. ❌ 移除了右侧日志控制区域")
        print("2. ✅ 统计信息以彩色标签形式显示在顶部")
        print("3. ✅ 日志显示区域占据全部可用空间")
        print("4. ✅ 过滤功能通过弹出面板提供")
        print("5. ✅ 界面更加简洁和专注")
        
        print("\n📊 统计标签说明:")
        print("• 🔵 总计标签: 显示所有日志总数")
        print("• 🔴 错误标签: 显示错误级别日志数量")
        print("• 🟠 警告标签: 显示警告级别日志数量")
        print("• 🔵 信息标签: 显示信息级别日志数量")
        print("• ⚪ 调试标签: 显示调试级别日志数量")
        
        print("\n🎛️ 功能说明:")
        print("• 点击'过滤'按钮打开过滤设置面板")
        print("• 统计标签实时更新显示最新数据")
        print("• 工具栏提供搜索、清除、导出功能")
        print("• 日志显示区域支持颜色分级显示")
        
        print("\n⌨️ 按 Ctrl+C 退出测试")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
