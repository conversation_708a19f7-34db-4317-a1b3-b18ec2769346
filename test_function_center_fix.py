#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试功能中心修复效果
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dialog_imports():
    """测试对话框导入"""
    print("🧪 测试功能中心对话框导入...")
    
    dialogs = {
        "视频下载": "ui.video_download_dialog.VideoDownloadDialog",
        "视频提取音频": "ui.batch_audio_extraction_dialog.BatchAudioExtractionDialog", 
        "人声分离": "ui.voice_separation_dialog.VoiceSeparationDialog",
        "音频提取字幕": "ui.subtitle_extraction_dialog.SubtitleExtractionDialog",
        "字幕翻译": "ui.subtitle_translation_dialog.SubtitleTranslationDialog",
        "字幕配音": "ui.subtitle_voiceover_dialog.SubtitleVoiceoverDialog",
        "字幕编辑器": "ui.subtitle_editor_dialog.SubtitleEditorDialog"
    }
    
    results = {}
    
    for name, module_path in dialogs.items():
        try:
            module_name, class_name = module_path.rsplit('.', 1)
            module = __import__(module_name, fromlist=[class_name])
            dialog_class = getattr(module, class_name)
            
            print(f"✅ {name}: 导入成功")
            results[name] = True
            
        except ImportError as e:
            print(f"❌ {name}: 导入失败 - {e}")
            results[name] = False
        except Exception as e:
            print(f"❌ {name}: 其他错误 - {e}")
            results[name] = False
    
    return results

def test_function_center_methods():
    """测试功能中心方法"""
    print("\n🧪 测试功能中心方法...")

    try:
        from PySide6.QtWidgets import QApplication, QWidget
        from ui.fliptalk_ui import FunctionArea

        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建功能中心实例
        function_area = FunctionArea()

        # 测试方法是否存在
        methods = [
            "on_function_clicked",
            "show_video_download_dialog",
            "show_batch_audio_extraction_dialog",
            "show_voice_separation_dialog",
            "show_subtitle_extraction_dialog",
            "show_subtitle_translation_dialog",
            "show_subtitle_voiceover_dialog",
            "show_subtitle_editor_dialog"
        ]

        method_results = {}

        for method_name in methods:
            if hasattr(function_area, method_name):
                print(f"✅ {method_name}: 方法存在")
                method_results[method_name] = True
            else:
                print(f"❌ {method_name}: 方法不存在")
                method_results[method_name] = False

        return method_results

    except Exception as e:
        print(f"❌ 测试功能中心方法失败: {e}")
        return {}

def test_function_center_click_simulation():
    """模拟功能中心点击测试"""
    print("\n🧪 模拟功能中心点击测试...")

    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import FunctionArea

        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建功能中心实例
        function_area = FunctionArea()

        # 模拟点击不同功能
        functions = [
            "视频下载",
            "视频提取音频",
            "人声分离",
            "音频提取字幕",
            "字幕翻译",
            "字幕配音"
        ]

        click_results = {}

        for function_name in functions:
            try:
                print(f"🖱️ 模拟点击: {function_name}")
                function_area.on_function_clicked(function_name)
                print(f"✅ {function_name}: 点击处理成功")
                click_results[function_name] = True

            except Exception as e:
                print(f"❌ {function_name}: 点击处理失败 - {e}")
                click_results[function_name] = False

        return click_results

    except Exception as e:
        print(f"❌ 模拟点击测试失败: {e}")
        return {}

def check_dialog_files():
    """检查对话框文件是否存在"""
    print("\n📁 检查对话框文件...")
    
    dialog_files = [
        "ui/video_download_dialog.py",
        "ui/batch_audio_extraction_dialog.py",
        "ui/voice_separation_dialog.py", 
        "ui/subtitle_extraction_dialog.py",
        "ui/subtitle_translation_dialog.py",
        "ui/subtitle_voiceover_dialog.py",
        "ui/subtitle_editor_dialog.py"
    ]
    
    file_results = {}
    
    for file_path in dialog_files:
        exists = os.path.exists(file_path)
        file_name = os.path.basename(file_path)
        
        if exists:
            file_size = os.path.getsize(file_path)
            print(f"✅ {file_name}: 存在 ({file_size} bytes)")
            file_results[file_name] = True
        else:
            print(f"❌ {file_name}: 不存在")
            file_results[file_name] = False
    
    return file_results

def main():
    """主函数"""
    print("🚀 功能中心修复测试")
    print("=" * 60)
    
    # 1. 检查对话框文件
    file_results = check_dialog_files()
    
    # 2. 测试对话框导入
    import_results = test_dialog_imports()
    
    # 3. 测试功能中心方法
    method_results = test_function_center_methods()
    
    # 4. 模拟点击测试
    click_results = test_function_center_click_simulation()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 测试结果汇总:")
    
    all_success = True
    
    print(f"\n📁 文件检查: {sum(file_results.values())}/{len(file_results)} 通过")
    if not all(file_results.values()):
        all_success = False
    
    print(f"📦 导入测试: {sum(import_results.values())}/{len(import_results)} 通过")
    if not all(import_results.values()):
        all_success = False
    
    if method_results:
        print(f"🔧 方法测试: {sum(method_results.values())}/{len(method_results)} 通过")
        if not all(method_results.values()):
            all_success = False
    
    if click_results:
        print(f"🖱️ 点击测试: {sum(click_results.values())}/{len(click_results)} 通过")
        if not all(click_results.values()):
            all_success = False
    
    if all_success:
        print("\n✅ 功能中心修复成功！所有功能都可以正常启动")
    else:
        print("\n❌ 功能中心仍有问题，请检查失败的项目")
    
    print("\n💡 使用说明:")
    print("1. 启动FlipTalk AI应用")
    print("2. 点击功能中心页面")
    print("3. 点击任意功能卡片的'立即使用'按钮")
    print("4. 对应的功能对话框应该正常打开")

if __name__ == "__main__":
    main()
