#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底调查背景音乐问题
使用实际的文件路径进行测试
"""

import os
import sys
from pathlib import Path

def analyze_actual_files():
    """分析实际的文件"""
    print("🔍 分析实际文件...")
    
    # 实际文件路径
    background_file = r"J:\MyAi\03 FlipTalk Ai\output\voice_separation\Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav"
    voiceover_file = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\combined_voiceover.wav"
    final_video = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\Tutorial_Vectorworks_Plugin_for_crowdit1080p_translated_20250728_152936.mp4"
    
    print(f"📂 背景音乐文件: {background_file}")
    print(f"📂 配音文件: {voiceover_file}")
    print(f"📂 最终视频: {final_video}")
    
    # 检查文件存在性
    files_status = {}
    for name, path in [("背景音乐", background_file), ("配音", voiceover_file), ("最终视频", final_video)]:
        exists = os.path.exists(path)
        size = os.path.getsize(path) if exists else 0
        files_status[name] = {"exists": exists, "size": size, "path": path}
        print(f"{'✅' if exists else '❌'} {name}: {size/(1024*1024):.2f}MB" if exists else f"❌ {name}: 不存在")
    
    return files_status

def test_background_music_mixing():
    """测试背景音乐混合"""
    print("\n🧪 测试背景音乐混合...")
    
    try:
        from pydub import AudioSegment
        import datetime
        
        # 实际文件路径
        background_file = r"J:\MyAi\03 FlipTalk Ai\output\voice_separation\Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav"
        voiceover_file = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\combined_voiceover.wav"
        
        print("📂 加载音频文件...")
        voiceover_audio = AudioSegment.from_wav(voiceover_file)
        background_music = AudioSegment.from_file(background_file)
        
        print(f"📊 配音音频: 长度={len(voiceover_audio)/1000:.2f}s, 音量={voiceover_audio.dBFS:.1f}dB")
        print(f"📊 背景音乐: 长度={len(background_music)/1000:.2f}s, 音量={background_music.dBFS:.1f}dB")
        
        # 调整背景音乐长度
        voiceover_duration = len(voiceover_audio)
        background_duration = len(background_music)
        
        if background_duration > voiceover_duration:
            background_music = background_music[:voiceover_duration]
            print(f"🔧 背景音乐已截取到 {voiceover_duration/1000:.2f}s")
        elif background_duration < voiceover_duration:
            repeat_times = int(voiceover_duration / background_duration) + 1
            background_music = background_music * repeat_times
            background_music = background_music[:voiceover_duration]
            print(f"🔧 背景音乐已循环 {repeat_times} 次")
        
        # 应用修复后的音量调整逻辑
        bg_current_db = background_music.dBFS
        voiceover_db = voiceover_audio.dBFS
        
        print(f"📊 原始音量 - 配音: {voiceover_db:.1f}dB, 背景音乐: {bg_current_db:.1f}dB")
        
        # 计算目标背景音乐音量（相对于配音音量降低7dB）
        target_bg_db = voiceover_db - 7
        gain_adjustment = target_bg_db - bg_current_db
        
        # 应用增益调整
        background_music = background_music + gain_adjustment
        adjusted_bg_db = background_music.dBFS
        
        print(f"🔧 音量调整: {gain_adjustment:+.1f}dB")
        print(f"📊 调整后音量 - 配音: {voiceover_db:.1f}dB, 背景音乐: {adjusted_bg_db:.1f}dB")
        
        # 混合音频
        print("🎵 混合音频...")
        mixed_audio = voiceover_audio.overlay(background_music)
        
        # 检查混合结果
        mixed_db = mixed_audio.dBFS
        print(f"📊 混合后音频: 长度={len(mixed_audio)/1000:.2f}s, 音量={mixed_db:.1f}dB")
        
        # 防止削波和音量优化
        if mixed_db > -3:
            volume_adjustment = -3 - mixed_db
            mixed_audio = mixed_audio + volume_adjustment
            print(f"🔧 防削波调整: {volume_adjustment:.1f}dB")
        elif mixed_db < -18:
            volume_boost = -15 - mixed_db
            mixed_audio = mixed_audio + volume_boost
            print(f"🔧 音量提升: {volume_boost:.1f}dB")
        
        # 导出测试混合文件
        output_dir = r"J:\MyAi\03 FlipTalk Ai\output\synthesis"
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        mixed_filename = f"test_mixed_audio_with_background_{timestamp}.wav"
        mixed_audio_path = os.path.join(output_dir, mixed_filename)
        
        mixed_audio.export(mixed_audio_path, format="wav")
        
        final_db = mixed_audio.dBFS
        print(f"✅ 测试混合完成!")
        print(f"📁 输出文件: {mixed_audio_path}")
        print(f"📊 最终音频: 音量={final_db:.1f}dB, 时长={len(mixed_audio)/1000:.2f}s")
        
        return mixed_audio_path
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_final_video_audio():
    """分析最终视频的音频轨道"""
    print("\n🎬 分析最终视频音频轨道...")
    
    final_video = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\Tutorial_Vectorworks_Plugin_for_crowdit1080p_translated_20250728_152936.mp4"
    
    if not os.path.exists(final_video):
        print("❌ 最终视频文件不存在")
        return
    
    try:
        import subprocess
        
        # 使用FFprobe分析视频音频信息
        cmd = [
            "ffprobe",
            "-v", "quiet",
            "-print_format", "json",
            "-show_streams",
            final_video
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)
            
            print("📊 视频流信息:")
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'audio':
                    print(f"🔊 音频流:")
                    print(f"   - 编解码器: {stream.get('codec_name', 'unknown')}")
                    print(f"   - 采样率: {stream.get('sample_rate', 'unknown')}Hz")
                    print(f"   - 声道数: {stream.get('channels', 'unknown')}")
                    print(f"   - 比特率: {stream.get('bit_rate', 'unknown')} bps")
                    print(f"   - 时长: {stream.get('duration', 'unknown')}s")
                elif stream.get('codec_type') == 'video':
                    print(f"🎥 视频流:")
                    print(f"   - 编解码器: {stream.get('codec_name', 'unknown')}")
                    print(f"   - 分辨率: {stream.get('width', 'unknown')}x{stream.get('height', 'unknown')}")
                    print(f"   - 帧率: {stream.get('r_frame_rate', 'unknown')}")
        else:
            print(f"❌ FFprobe执行失败: {result.stderr}")
    
    except Exception as e:
        print(f"❌ 分析视频失败: {e}")

def extract_audio_from_video():
    """从最终视频中提取音频进行分析"""
    print("\n🔊 从最终视频提取音频...")
    
    final_video = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\Tutorial_Vectorworks_Plugin_for_crowdit1080p_translated_20250728_152936.mp4"
    output_audio = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\extracted_audio_from_final_video.wav"
    
    try:
        import subprocess
        
        cmd = [
            "ffmpeg",
            "-i", final_video,
            "-vn",  # 不要视频
            "-acodec", "pcm_s16le",  # 转换为WAV
            "-ar", "44100",  # 采样率
            "-ac", "2",  # 立体声
            "-y",  # 覆盖输出文件
            output_audio
        ]
        
        print(f"🔧 FFmpeg命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✅ 音频提取成功: {output_audio}")
            
            # 分析提取的音频
            try:
                from pydub import AudioSegment
                extracted_audio = AudioSegment.from_wav(output_audio)
                print(f"📊 提取的音频: 长度={len(extracted_audio)/1000:.2f}s, 音量={extracted_audio.dBFS:.1f}dB")
                
                return output_audio
            except Exception as e:
                print(f"❌ 分析提取音频失败: {e}")
                return None
        else:
            print(f"❌ 音频提取失败: {result.stderr}")
            return None
    
    except Exception as e:
        print(f"❌ 提取音频异常: {e}")
        return None

def compare_audio_files():
    """比较不同的音频文件"""
    print("\n📊 比较音频文件...")
    
    files = {
        "原始背景音乐": r"J:\MyAi\03 FlipTalk Ai\output\voice_separation\Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio_background.wav",
        "配音文件": r"J:\MyAi\03 FlipTalk Ai\output\synthesis\combined_voiceover.wav",
    }
    
    # 检查是否有测试混合文件
    synthesis_dir = r"J:\MyAi\03 FlipTalk Ai\output\synthesis"
    for file in os.listdir(synthesis_dir):
        if "test_mixed_audio_with_background" in file:
            files["测试混合音频"] = os.path.join(synthesis_dir, file)
            break
    
    # 检查是否有提取的音频
    extracted_audio = r"J:\MyAi\03 FlipTalk Ai\output\synthesis\extracted_audio_from_final_video.wav"
    if os.path.exists(extracted_audio):
        files["最终视频音频"] = extracted_audio
    
    try:
        from pydub import AudioSegment
        
        print("📋 音频文件对比:")
        for name, path in files.items():
            if os.path.exists(path):
                try:
                    audio = AudioSegment.from_file(path)
                    print(f"🎵 {name}: {audio.dBFS:.1f}dB, {len(audio)/1000:.1f}s")
                except Exception as e:
                    print(f"❌ {name}: 无法分析 - {e}")
            else:
                print(f"❌ {name}: 文件不存在")
    
    except ImportError:
        print("❌ 缺少pydub库")

def main():
    """主函数"""
    print("🚀 彻底调查背景音乐问题")
    print("=" * 60)
    
    # 1. 分析实际文件
    files_status = analyze_actual_files()
    
    # 2. 测试背景音乐混合
    mixed_file = test_background_music_mixing()
    
    # 3. 分析最终视频
    analyze_final_video_audio()
    
    # 4. 从视频提取音频
    extracted_audio = extract_audio_from_video()
    
    # 5. 比较所有音频文件
    compare_audio_files()
    
    print("\n" + "=" * 60)
    print("🔍 调查结论:")
    
    if mixed_file:
        print("✅ 背景音乐混合功能正常工作")
        print("❌ 但是在实际视频合成过程中，背景音乐混合步骤没有执行")
        print("💡 问题可能在于:")
        print("   1. UI中的背景音乐选项检查逻辑有问题")
        print("   2. 背景音乐混合函数没有被调用")
        print("   3. 混合后的音频文件没有被正确传递给FFmpeg")
    else:
        print("❌ 背景音乐混合功能本身有问题")
    
    print(f"\n📁 请检查synthesis目录中是否有mixed_audio_with_background_*.wav文件")
    print(f"📁 如果没有，说明背景音乐混合步骤根本没有执行")

if __name__ == "__main__":
    main()
