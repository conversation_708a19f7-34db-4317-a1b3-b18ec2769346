#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试视频合成过程中的背景音乐问题
模拟完整的视频合成流程
"""

import os
import sys
import subprocess
from pathlib import Path

def check_background_music_path():
    """检查背景音乐路径获取逻辑"""
    print("🔍 检查背景音乐路径获取...")
    
    # 模拟 get_voice_separation_output_dir 逻辑
    output_dir = "output"
    voice_separation_dir = os.path.join(output_dir, "voice_separation")
    
    print(f"📂 人声分离目录: {voice_separation_dir}")
    
    if not os.path.exists(voice_separation_dir):
        print("❌ 人声分离目录不存在")
        return None
    
    # 模拟 get_background_music_path 逻辑
    possible_patterns = [
        "*background*.wav",
        "*background*.mp3", 
        "*other*.wav",
        "*other*.mp3",
        "*instrumental*.wav",
        "*instrumental*.mp3"
    ]
    
    import glob
    for pattern in possible_patterns:
        files = glob.glob(os.path.join(voice_separation_dir, pattern))
        if files:
            latest_file = max(files, key=os.path.getmtime)
            print(f"✅ 找到背景音乐文件: {latest_file}")
            return latest_file
    
    print("❌ 未找到背景音乐文件")
    return None

def validate_background_music_file(file_path):
    """验证背景音乐文件"""
    print(f"🔍 验证背景音乐文件: {file_path}")
    
    if not os.path.exists(file_path):
        print("❌ 文件不存在")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"📊 文件大小: {file_size} bytes ({file_size/(1024*1024):.2f} MB)")
    
    if file_size < 1024:
        print("❌ 文件太小")
        return False
    
    # 检查音频文件
    try:
        from pydub import AudioSegment
        audio = AudioSegment.from_file(file_path)
        
        duration = len(audio)
        volume_db = audio.dBFS
        
        print(f"📊 音频时长: {duration}ms ({duration/1000:.2f}s)")
        print(f"📊 音频音量: {volume_db:.1f}dB")
        
        if duration < 1000:
            print("❌ 音频太短")
            return False
        
        if volume_db == float('-inf'):
            print("❌ 音频为静音")
            return False
        
        print("✅ 背景音乐文件有效")
        return True
        
    except Exception as e:
        print(f"❌ 音频验证失败: {e}")
        return False

def simulate_background_music_mixing():
    """模拟背景音乐混合过程"""
    print("\n🧪 模拟背景音乐混合过程...")
    
    # 获取文件路径
    background_music_path = check_background_music_path()
    if not background_music_path:
        print("❌ 无法获取背景音乐路径")
        return None
    
    # 验证背景音乐文件
    if not validate_background_music_file(background_music_path):
        print("❌ 背景音乐文件无效")
        return None
    
    # 检查配音文件
    voiceover_path = "output/synthesis/combined_voiceover.wav"
    if not os.path.exists(voiceover_path):
        print(f"❌ 配音文件不存在: {voiceover_path}")
        return None
    
    print(f"✅ 配音文件存在: {voiceover_path}")
    
    try:
        from pydub import AudioSegment
        import datetime
        
        print("📂 加载音频文件...")
        
        # 加载音频
        voiceover_audio = AudioSegment.from_wav(voiceover_path)
        background_music = AudioSegment.from_file(background_music_path)
        
        print(f"📊 配音音频: 长度={len(voiceover_audio)/1000:.2f}s, 音量={voiceover_audio.dBFS:.1f}dB")
        print(f"📊 背景音乐: 长度={len(background_music)/1000:.2f}s, 音量={background_music.dBFS:.1f}dB")
        
        # 调整背景音乐长度
        voiceover_duration = len(voiceover_audio)
        background_duration = len(background_music)
        
        if background_duration > voiceover_duration:
            background_music = background_music[:voiceover_duration]
            print(f"🔧 背景音乐已截取到 {voiceover_duration/1000:.2f}s")
        elif background_duration < voiceover_duration:
            repeat_times = int(voiceover_duration / background_duration) + 1
            background_music = background_music * repeat_times
            background_music = background_music[:voiceover_duration]
            print(f"🔧 背景音乐已循环 {repeat_times} 次")
        
        # 应用修复后的音量调整逻辑
        bg_current_db = background_music.dBFS
        voiceover_db = voiceover_audio.dBFS
        
        print(f"📊 配音音量: {voiceover_db:.1f}dB, 背景音乐音量: {bg_current_db:.1f}dB")
        
        # 计算目标背景音乐音量（相对于配音音量降低7dB）
        target_bg_db = voiceover_db - 7
        gain_adjustment = target_bg_db - bg_current_db
        
        # 应用增益调整
        background_music = background_music + gain_adjustment
        adjusted_bg_db = background_music.dBFS
        
        print(f"🔧 背景音乐音量调整: {gain_adjustment:+.1f}dB (从 {bg_current_db:.1f}dB 到 {adjusted_bg_db:.1f}dB)")
        
        # 混合音频
        print("🎵 正在混合音频...")
        mixed_audio = voiceover_audio.overlay(background_music)
        
        # 检查混合后的音频质量
        mixed_db = mixed_audio.dBFS
        print(f"📊 混合后音频: 长度={len(mixed_audio)/1000:.2f}s, 音量={mixed_db:.1f}dB")
        
        # 防止削波
        if mixed_db > -3:
            volume_adjustment = -3 - mixed_db
            mixed_audio = mixed_audio + volume_adjustment
            print(f"🔧 混合音频音量已调整 {volume_adjustment:.1f}dB 以避免削波")
        
        # 如果混合后音频过小，进行适度增益
        elif mixed_db < -18:
            volume_boost = -15 - mixed_db
            mixed_audio = mixed_audio + volume_boost
            print(f"🔧 混合音频音量已提升 {volume_boost:.1f}dB 以改善听感")
        
        # 导出混合后的音频
        output_dir = "output/synthesis"
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        mixed_filename = f"debug_mixed_audio_with_background_{timestamp}.wav"
        mixed_audio_path = os.path.join(output_dir, mixed_filename)
        
        mixed_audio.export(mixed_audio_path, format="wav")
        
        final_db = mixed_audio.dBFS
        print(f"✅ 背景音乐混合完成: {mixed_filename}")
        print(f"🎵 最终音频质量: 音量={final_db:.1f}dB, 时长={len(mixed_audio)/1000:.2f}s")
        
        return mixed_audio_path
        
    except ImportError:
        print("❌ 缺少 pydub 库，无法混合背景音乐")
        return None
    except Exception as e:
        print(f"❌ 混合背景音乐失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def simulate_video_audio_replacement(audio_path):
    """模拟视频音频替换过程"""
    print(f"\n🎬 模拟视频音频替换过程...")
    
    # 查找原始视频文件
    video_files = []
    for ext in ['.mp4', '.avi', '.mov', '.mkv']:
        import glob
        files = glob.glob(f"**/*{ext}", recursive=True)
        video_files.extend(files)
    
    if not video_files:
        print("❌ 未找到视频文件")
        return None
    
    # 使用第一个找到的视频文件
    video_path = video_files[0]
    print(f"📁 使用视频文件: {video_path}")
    print(f"📁 使用音频文件: {audio_path}")
    
    # 检查输入音频文件
    try:
        from pydub import AudioSegment
        test_audio = AudioSegment.from_wav(audio_path)
        print(f"🔍 输入音频检查: 长度={len(test_audio)/1000:.2f}s, 音量={test_audio.dBFS:.1f}dB")
        
        if test_audio.dBFS == float('-inf'):
            print("❌ 输入音频为静音，无法进行视频合成")
            return None
    except Exception as e:
        print(f"❌ 无法读取输入音频文件: {e}")
        return None
    
    # 生成输出文件名
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    original_name = os.path.splitext(os.path.basename(video_path))[0]
    output_filename = f"{original_name}_debug_translated_{timestamp}.mp4"
    output_video_path = os.path.join("output/synthesis", output_filename)
    
    # 构建FFmpeg命令
    ffmpeg_cmd = [
        "ffmpeg",
        "-i", video_path,           # 输入视频
        "-i", audio_path,           # 输入音频
        "-c:v", "copy",             # 复制视频流，不重新编码
        "-c:a", "aac",              # 音频编码为AAC
        "-b:a", "192k",             # 音频比特率
        "-ar", "44100",             # 音频采样率
        "-ac", "2",                 # 立体声
        "-map", "0:v:0",            # 使用第一个输入的视频流
        "-map", "1:a:0",            # 使用第二个输入的音频流
        "-shortest",                # 以最短的流为准
        "-avoid_negative_ts", "make_zero",  # 避免负时间戳
        "-y",                       # 覆盖输出文件
        output_video_path
    ]
    
    print(f"🔧 FFmpeg命令: {' '.join(ffmpeg_cmd)}")
    
    # 执行FFmpeg命令
    try:
        print("⚙️ 正在执行FFmpeg...")
        result = subprocess.run(
            ffmpeg_cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print(f"✅ 视频音频替换成功!")
            print(f"📁 输出文件: {output_video_path}")
            
            # 检查输出文件
            if os.path.exists(output_video_path):
                file_size = os.path.getsize(output_video_path)
                size_mb = file_size / (1024 * 1024)
                print(f"📊 输出文件大小: {size_mb:.1f} MB")
                return output_video_path
            else:
                print("❌ 输出文件不存在")
                return None
        else:
            print(f"❌ FFmpeg执行失败 (返回码: {result.returncode})")
            print(f"错误输出: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ FFmpeg执行超时")
        return None
    except Exception as e:
        print(f"❌ FFmpeg执行异常: {e}")
        return None

def main():
    """主函数"""
    print("🚀 调试视频合成过程中的背景音乐问题")
    print("=" * 60)
    
    # 第一步：检查背景音乐混合
    mixed_audio_path = simulate_background_music_mixing()
    
    if not mixed_audio_path:
        print("\n❌ 背景音乐混合失败，这就是问题所在！")
        print("\n💡 可能的原因:")
        print("1. 背景音乐文件不存在或无效")
        print("2. pydub库缺失或音频格式不支持")
        print("3. 音频文件损坏或为静音")
        print("4. 文件权限问题")
        return
    
    print(f"\n✅ 背景音乐混合成功: {mixed_audio_path}")
    
    # 第二步：检查视频音频替换
    final_video_path = simulate_video_audio_replacement(mixed_audio_path)
    
    if not final_video_path:
        print("\n❌ 视频音频替换失败")
        print("\n💡 可能的原因:")
        print("1. FFmpeg不可用或版本不兼容")
        print("2. 视频文件格式不支持")
        print("3. 音频编码问题")
        print("4. 文件路径或权限问题")
        return
    
    print(f"\n✅ 视频合成完全成功: {final_video_path}")
    print("\n🎵 如果您的实际操作中背景音乐仍然缺失，请检查:")
    print("1. 是否真的勾选了'保留背景音乐'选项")
    print("2. 控制台是否显示'🎵 开始混合背景音乐...'消息")
    print("3. 是否有'⚠️ 背景音乐混合失败，使用纯配音音频'警告")
    print("4. synthesis目录中是否有mixed_audio_with_background_*.wav文件")

if __name__ == "__main__":
    main()
