# FlipTalk AI 背景音乐功能使用指南

## 🎵 功能概述

FlipTalk AI 支持在生成翻译视频时保留原视频的背景音乐，让翻译后的视频既有清晰的配音，又保持原有的音乐氛围。

## 📋 使用步骤

### 1. 启用人声分离功能
在主界面的**选项设置**中：
- ✅ 勾选 **"人声分离"** 选项
- 这将在处理视频时自动分离人声和背景音乐

### 2. 执行人声分离
- 上传视频文件后，系统会自动进行人声分离
- 分离完成后会生成两个文件：
  - `*_vocals.wav` - 人声文件
  - `*_background.wav` - 背景音乐文件

### 3. 启用背景音乐保留
在**视频合成选项**中：
- ✅ 勾选 **"保留背景音乐"** 选项
- 系统会自动验证是否满足保留条件

### 4. 生成翻译视频
- 完成字幕翻译和配音设置后
- 点击 **"生成视频"** 按钮
- 系统会自动混合配音和背景音乐

## 🔧 技术细节

### 音量调整逻辑
- **配音音量**: 保持原始生成音量
- **背景音乐音量**: 自动调整为配音音量的 45%（降低 7dB）
- **混合后音量**: 自动优化，避免削波和过小

### 文件输出
生成的文件包括：
- `combined_voiceover.wav` - 纯配音音频
- `mixed_audio_with_background_*.wav` - 混合音频（配音+背景音乐）
- `*_translated_*.mp4` - 最终翻译视频

## ⚠️ 注意事项

### 必要条件
1. **必须启用人声分离**: 没有人声分离就无法获得背景音乐文件
2. **背景音乐文件有效**: 文件大小 > 1KB，时长 > 1秒
3. **勾选保留选项**: 必须在视频合成时勾选"保留背景音乐"

### 常见问题

#### Q: 为什么听不到背景音乐？
A: 检查以下几点：
- 是否勾选了"保留背景音乐"选项
- 原视频是否真的有背景音乐
- 人声分离是否成功生成背景音乐文件

#### Q: 背景音乐太大声或太小声？
A: 系统会自动调整音量比例：
- 背景音乐音量 = 配音音量 - 7dB
- 如需手动调整，可以修改源码中的 `target_bg_db = voiceover_db - 7` 参数

#### Q: 背景音乐和配音不同步？
A: 系统会自动处理同步：
- 背景音乐会自动截取或循环以匹配配音长度
- 使用 `-shortest` 参数确保视频同步

## 🛠️ 故障排除

### 诊断工具
运行诊断脚本检查问题：
```bash
python test_background_music_diagnosis.py output
```

### 测试修复
运行测试脚本验证功能：
```bash
python test_background_music_fix.py
```

### 手动检查
1. **检查人声分离输出**:
   ```
   output/voice_separation/
   ├── *_vocals.wav      # 人声文件
   └── *_background.wav  # 背景音乐文件
   ```

2. **检查合成输出**:
   ```
   output/synthesis/
   ├── combined_voiceover.wav              # 配音文件
   ├── mixed_audio_with_background_*.wav   # 混合音频
   └── *_translated_*.mp4                  # 最终视频
   ```

3. **音量水平检查**:
   - 配音音量: 通常在 -15dB 到 -25dB 之间
   - 背景音乐音量: 应比配音低 7dB 左右
   - 混合音频音量: 应略高于配音音量

## 📊 音量参考标准

| 音频类型 | 理想音量范围 | 说明 |
|---------|-------------|------|
| 配音 | -15dB ~ -25dB | 清晰可听 |
| 背景音乐 | -22dB ~ -32dB | 配音的45% |
| 混合音频 | -12dB ~ -22dB | 整体平衡 |

## 🎯 最佳实践

1. **选择合适的原视频**: 确保原视频有明显的背景音乐
2. **检查人声分离质量**: 分离后的背景音乐应该清晰无人声
3. **预览混合效果**: 可以播放生成的混合音频文件预览效果
4. **调整参数**: 如需要可以修改音量比例参数

## 🔄 更新日志

### v1.1 (2025-01-28)
- ✅ 修复背景音乐音量过低问题
- ✅ 优化音量调整算法
- ✅ 改进音频混合质量
- ✅ 添加详细的诊断工具

### v1.0
- ✅ 基础背景音乐保留功能
- ✅ 人声分离集成
- ✅ 自动音量调整

---

如有问题，请查看控制台输出或运行诊断工具获取详细信息。
