#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试美化后的日志界面
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_beautiful_ui():
    """测试美化后的UI"""
    print("🎨 测试美化后的日志界面...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from ui.log_area import LogArea
        from core.logger import get_logger, LogLevel
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建日志区域
        log_area = LogArea()
        log_area.show()
        
        # 获取日志管理器
        logger = get_logger()
        
        # 添加一些测试日志
        test_logs = [
            (LogLevel.INFO, "System", "FlipTalk AI 系统启动"),
            (LogLevel.INFO, "GPU", "检测到 NVIDIA GeForce RTX 4090"),
            (LogLevel.WARNING, "Memory", "GPU内存使用率较高: 85%"),
            (LogLevel.INFO, "Plugin", "加载音频提取插件成功"),
            (LogLevel.INFO, "Plugin", "加载人声分离插件成功"),
            (LogLevel.ERROR, "Network", "网络连接超时，请检查网络设置"),
            (LogLevel.DEBUG, "Cache", "清理临时文件缓存"),
            (LogLevel.INFO, "TTS", "Edge TTS 引擎初始化完成"),
            (LogLevel.WARNING, "Model", "模型文件较大，加载可能需要时间"),
            (LogLevel.INFO, "Audio", "音频处理完成: sample_rate=44100Hz"),
            (LogLevel.ERROR, "File", "无法读取视频文件: 文件格式不支持"),
            (LogLevel.INFO, "Export", "字幕导出成功: output.srt"),
            (LogLevel.DEBUG, "Performance", "处理耗时: 2.34秒"),
            (LogLevel.WARNING, "Disk", "磁盘空间不足，建议清理临时文件"),
            (LogLevel.INFO, "UI", "用户界面初始化完成")
        ]
        
        # 定时添加日志
        def add_test_log():
            if test_logs:
                level, module, message = test_logs.pop(0)
                logger.add_log(level, module, message)
                
                # 继续添加下一条日志
                if test_logs:
                    QTimer.singleShot(500, add_test_log)
        
        # 开始添加测试日志
        QTimer.singleShot(1000, add_test_log)
        
        print("✅ 美化后的日志界面已启动")
        print("💡 界面特色:")
        print("  🎨 现代化卡片设计")
        print("  🌈 渐变色彩搭配")
        print("  🔍 高级搜索功能")
        print("  📊 实时统计信息")
        print("  🎛️ 智能过滤控制")
        print("  ⏰ 实时时间显示")
        
        print("\n🖱️ 交互功能:")
        print("  • 点击级别复选框切换过滤")
        print("  • 在搜索框中输入关键词")
        print("  • 使用导航按钮跳转搜索结果")
        print("  • 点击清除/导出按钮操作日志")
        
        print("\n⌨️ 按 Ctrl+C 退出测试")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试美化UI失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n🧪 测试UI组件...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.log_area import LogArea, LogDisplayWidget, LogFilterWidget, LogToolbarWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试各个组件
        components = {
            "LogArea": LogArea,
            "LogDisplayWidget": LogDisplayWidget,
            "LogFilterWidget": LogFilterWidget,
            "LogToolbarWidget": LogToolbarWidget
        }
        
        success = True
        for name, component_class in components.items():
            try:
                component = component_class()
                print(f"  ✅ {name}: 创建成功")
                
                # 检查样式是否应用
                if hasattr(component, 'styleSheet') and component.styleSheet():
                    print(f"    🎨 样式已应用")
                
            except Exception as e:
                print(f"  ❌ {name}: 创建失败 - {e}")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 测试UI组件失败: {e}")
        return False

def test_color_scheme():
    """测试颜色方案"""
    print("\n🎨 测试颜色方案...")
    
    try:
        # 检查颜色定义
        colors = {
            "主题蓝色": "#4A9EFF",
            "错误红色": "#FF6B6B", 
            "警告橙色": "#FFA726",
            "成功绿色": "#27AE60",
            "调试灰色": "#CCCCCC",
            "深色背景": "#1E1E1E",
            "卡片背景": "#2A2A2A",
            "边框颜色": "#3A3A3A"
        }
        
        # 读取样式文件检查颜色使用
        with open("ui/log_area.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_colors = []
        for color_name, color_code in colors.items():
            if color_code not in content:
                missing_colors.append(color_name)
            else:
                count = content.count(color_code)
                print(f"  ✅ {color_name} ({color_code}): 使用 {count} 次")
        
        if missing_colors:
            print(f"  ⚠️ 未使用的颜色: {', '.join(missing_colors)}")
        
        print("✅ 颜色方案检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试颜色方案失败: {e}")
        return False

def test_responsive_design():
    """测试响应式设计"""
    print("\n📱 测试响应式设计...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.log_area import LogArea
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建日志区域
        log_area = LogArea()
        
        # 检查固定尺寸
        size = log_area.size()
        print(f"  📏 日志区域尺寸: {size.width()} x {size.height()}")
        
        # 检查布局
        layout = log_area.layout()
        if layout:
            print(f"  📐 布局类型: {type(layout).__name__}")
            print(f"  📦 子组件数量: {layout.count()}")
        
        print("✅ 响应式设计检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试响应式设计失败: {e}")
        return False

def main():
    """主函数"""
    print("🎨 FlipTalk AI 美化日志界面测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("UI组件", test_ui_components),
        ("颜色方案", test_color_scheme),
        ("响应式设计", test_responsive_design)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 美化界面测试通过！")
        print("\n🎨 美化特色:")
        print("1. ✅ 现代化卡片设计 - 圆角、阴影、渐变")
        print("2. ✅ 专业配色方案 - 深色主题，护眼舒适")
        print("3. ✅ 直观的视觉层次 - 清晰的信息组织")
        print("4. ✅ 交互式组件 - 悬停效果，状态反馈")
        print("5. ✅ 响应式布局 - 自适应不同内容")
        print("6. ✅ 专业字体 - 等宽字体，易于阅读")
        
        print("\n💡 设计亮点:")
        print("  🎯 渐变标题卡片 - 吸引注意力")
        print("  🔘 圆形操作按钮 - 现代化交互")
        print("  📊 彩色级别指示器 - 快速识别")
        print("  🎛️ 卡片式过滤面板 - 清晰分组")
        print("  ⏰ 实时状态显示 - 动态信息")
        
        # 启动交互式测试
        print(f"\n🚀 启动交互式界面测试...")
        test_beautiful_ui()
        
    else:
        print(f"\n❌ 美化界面有 {total_tests - passed_tests} 个问题需要修复")

if __name__ == "__main__":
    main()
