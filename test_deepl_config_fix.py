#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的DeepL API密钥配置功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_settings_save():
    """测试API设置界面的保存功能"""
    print("🔧 测试API设置界面的保存功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.api_settings import APISettingsArea
        from core.config_manager import get_config_manager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建API设置组件
        api_settings = APISettingsArea()
        print("✅ API设置组件创建成功")
        
        # 测试配置管理器连接
        config_manager = get_config_manager()
        
        # 模拟保存DeepL API密钥
        test_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
        print(f"🧪 测试保存DeepL API密钥: {test_key}")
        
        # 直接调用保存方法
        from PySide6.QtWidgets import QLineEdit
        mock_input = QLineEdit()
        mock_input.setText(test_key)
        
        api_settings.save_api_setting("DeepL翻译", "API密钥", mock_input)
        
        # 验证保存结果
        saved_key = config_manager.get("api_keys.deepl_api_key", "")
        if saved_key == test_key:
            print("✅ API设置保存功能正常工作")
            return True
        else:
            print(f"❌ API设置保存失败，期望: {test_key}，实际: {saved_key}")
            return False
        
    except Exception as e:
        print(f"❌ API设置保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_settings_load():
    """测试API设置界面的加载功能"""
    print("\n📥 测试API设置界面的加载功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.api_settings import APISettingsArea
        from core.config_manager import get_config_manager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 先保存一个测试密钥
        config_manager = get_config_manager()
        test_key = "test-load-key-12345"
        config_manager.update_api_key("deepl", "key", test_key)
        
        # 创建API设置组件（应该自动加载已保存的密钥）
        api_settings = APISettingsArea()
        
        # 测试load_saved_value方法
        loaded_value = api_settings.load_saved_value("DeepL翻译", "API密钥")
        
        if loaded_value == test_key:
            print("✅ API设置加载功能正常工作")
            return True
        else:
            print(f"❌ API设置加载失败，期望: {test_key}，实际: {loaded_value}")
            return False
        
    except Exception as e:
        print(f"❌ API设置加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_dialog_load():
    """测试字幕翻译对话框的密钥加载功能"""
    print("\n📝 测试字幕翻译对话框的密钥加载功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.subtitle_translation_dialog import SubtitleTranslationDialog
        from core.config_manager import get_config_manager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 先保存一个测试密钥
        config_manager = get_config_manager()
        test_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
        config_manager.update_api_key("deepl", "key", test_key)
        print(f"🔑 已保存测试密钥: {test_key}")
        
        # 创建字幕翻译对话框
        dialog = SubtitleTranslationDialog()
        print("✅ 字幕翻译对话框创建成功")
        
        # 检查配置管理器是否正确初始化
        if hasattr(dialog, 'config_manager'):
            print("✅ 配置管理器已正确初始化")
        else:
            print("❌ 配置管理器未初始化")
            return False
        
        # 模拟选择DeepL翻译器
        # 首先需要确保翻译器下拉框已创建
        if hasattr(dialog, 'translator_combo') and hasattr(dialog, 'api_key_edit'):
            # 查找DeepL选项
            for i in range(dialog.translator_combo.count()):
                if dialog.translator_combo.itemData(i) == "deepl":
                    dialog.translator_combo.setCurrentIndex(i)
                    break
            
            # 触发翻译器改变事件
            dialog.on_translator_changed()
            
            # 检查API密钥是否自动填充
            loaded_key = dialog.api_key_edit.text()
            if loaded_key == test_key:
                print("✅ 字幕翻译对话框自动加载密钥成功")
                return True
            else:
                print(f"❌ 字幕翻译对话框加载密钥失败，期望: {test_key}，实际: {loaded_key}")
                return False
        else:
            print("❌ 字幕翻译对话框控件未正确创建")
            return False
        
    except Exception as e:
        print(f"❌ 字幕翻译对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_workflow():
    """测试端到端工作流程"""
    print("\n🔄 测试端到端工作流程...")
    
    try:
        from core.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        # 1. 清除现有配置
        config_manager.update_api_key("deepl", "key", "")
        print("🧹 清除现有配置")
        
        # 2. 模拟用户在API设置界面保存密钥
        test_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
        success = config_manager.update_api_key("deepl", "key", test_key)
        if success:
            print(f"✅ 步骤1: API设置保存成功 - {test_key}")
        else:
            print("❌ 步骤1: API设置保存失败")
            return False
        
        # 3. 验证配置文件中的密钥
        saved_key = config_manager.get("api_keys.deepl_api_key", "")
        if saved_key == test_key:
            print("✅ 步骤2: 配置文件验证成功")
        else:
            print(f"❌ 步骤2: 配置文件验证失败，期望: {test_key}，实际: {saved_key}")
            return False
        
        # 4. 模拟字幕翻译对话框读取密钥
        loaded_key = config_manager.get("api_keys.deepl_api_key", "")
        if loaded_key == test_key:
            print("✅ 步骤3: 字幕翻译对话框读取成功")
        else:
            print(f"❌ 步骤3: 字幕翻译对话框读取失败，期望: {test_key}，实际: {loaded_key}")
            return False
        
        print("🎉 端到端工作流程测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 端到端工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI DeepL API密钥配置修复验证")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("API设置保存功能", test_api_settings_save),
        ("API设置加载功能", test_api_settings_load),
        ("字幕翻译对话框加载功能", test_translation_dialog_load),
        ("端到端工作流程", test_end_to_end_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 修复验证结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 DeepL API密钥配置问题已完全修复！")
        print("\n✅ 修复内容:")
        print("1. API设置界面现在可以正确保存DeepL API密钥到配置文件")
        print("2. API设置界面可以从配置文件加载已保存的密钥")
        print("3. 字幕翻译对话框可以自动从配置文件加载已保存的密钥")
        print("4. 配置存储和读取机制完全打通")
        
        print("\n🎯 使用说明:")
        print("1. 在API设置界面输入DeepL API密钥并点击保存")
        print("2. 打开字幕翻译对话框，选择DeepL翻译器")
        print("3. API密钥会自动填充到输入框中")
        print("4. 可以直接开始翻译，无需重复输入密钥")
    else:
        print(f"\n❌ 仍有 {total_tests - passed_tests} 个问题需要修复")

if __name__ == "__main__":
    main()
