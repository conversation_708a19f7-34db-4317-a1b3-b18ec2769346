#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证运行日志页面是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 FlipTalk AI 运行日志页面验证")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import FlipTalkMainWindow
        from core.logger import get_logger, log_info, log_warning, log_error, log_debug
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("📱 创建主窗口...")
        main_window = FlipTalkMainWindow()
        
        # 添加一些测试日志
        logger = get_logger()
        log_info("验证测试", "FlipTalk AI 运行日志页面验证开始")
        log_info("系统", "主窗口初始化完成")
        log_warning("内存", "GPU内存使用率: 75%")
        log_error("网络", "连接超时，请检查网络设置")
        log_debug("缓存", "清理临时文件缓存")
        log_info("插件", "音频提取插件加载成功")
        log_warning("磁盘", "磁盘空间不足，建议清理")
        log_info("TTS", "Edge TTS 引擎初始化完成")
        log_error("文件", "无法读取视频文件: 格式不支持")
        log_info("导出", "字幕导出成功: output.srt")
        
        print("📝 已添加10条测试日志")
        
        # 显示主窗口
        main_window.show()
        print("🖥️ 主窗口已显示")
        
        # 自动切换到日志页面
        print("🔄 自动切换到运行日志页面...")
        main_window.on_page_changed("运行日志")
        
        print("\n✅ 运行日志页面验证完成！")
        print("\n💡 功能说明:")
        print("1. 🖱️ 点击左侧导航栏的'运行日志'按钮")
        print("2. 📊 查看右侧的现代化日志界面")
        print("3. 🎛️ 使用右侧面板进行日志过滤")
        print("4. 🔍 使用工具栏搜索功能")
        print("5. 📤 点击导出按钮保存日志")
        print("6. 🗑️ 点击清除按钮清空日志")
        
        print("\n🎨 界面特色:")
        print("• 渐变蓝色标题卡片")
        print("• 现代化工具栏设计")
        print("• 分级颜色日志显示")
        print("• 右侧蓝色边框过滤面板")
        print("• 实时统计信息")
        print("• 底部状态栏和时间显示")
        
        print("\n⌨️ 按 Ctrl+C 退出验证")
        
        # 运行应用
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 验证结束")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
