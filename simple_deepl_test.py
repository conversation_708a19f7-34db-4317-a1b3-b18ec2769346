#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的DeepL API密钥配置测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_manager():
    """测试配置管理器的基本功能"""
    print("🔧 测试配置管理器...")
    
    try:
        from core.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        print("✅ 配置管理器初始化成功")
        
        # 测试保存DeepL API密钥
        test_key = "589a5a31-de6f-48c7-9653-690ac510bc35f"
        print(f"💾 保存测试密钥: {test_key}")
        
        success = config_manager.update_api_key("deepl", "key", test_key)
        if success:
            print("✅ 密钥保存成功")
        else:
            print("❌ 密钥保存失败")
            return False
        
        # 测试读取DeepL API密钥
        saved_key = config_manager.get("api_keys.deepl_api_key", "")
        print(f"📥 读取保存的密钥: {saved_key}")
        
        if saved_key == test_key:
            print("✅ 密钥读取验证成功")
            return True
        else:
            print(f"❌ 密钥读取验证失败，期望: {test_key}，实际: {saved_key}")
            return False
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_settings_methods():
    """测试API设置组件的方法"""
    print("\n🎛️ 测试API设置组件方法...")
    
    try:
        from ui.widgets.api_settings import APISettingsArea
        
        # 创建API设置组件（不显示UI）
        api_settings = APISettingsArea()
        print("✅ API设置组件创建成功")
        
        # 测试load_saved_value方法
        test_key = "test-method-key-12345"
        
        # 先保存一个测试值
        api_settings.config_manager.update_api_key("deepl", "key", test_key)
        
        # 测试加载方法
        loaded_value = api_settings.load_saved_value("DeepL翻译", "API密钥")
        print(f"📥 加载的值: {loaded_value}")
        
        if loaded_value == test_key:
            print("✅ load_saved_value方法工作正常")
            return True
        else:
            print(f"❌ load_saved_value方法失败，期望: {test_key}，实际: {loaded_value}")
            return False
        
    except Exception as e:
        print(f"❌ API设置组件方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_dialog_config():
    """测试字幕翻译对话框的配置加载"""
    print("\n📝 测试字幕翻译对话框配置加载...")
    
    try:
        # 先保存一个测试密钥
        from core.config_manager import get_config_manager
        config_manager = get_config_manager()
        test_key = "dialog-test-key-67890"
        config_manager.update_api_key("deepl", "key", test_key)
        print(f"💾 保存测试密钥: {test_key}")
        
        # 测试对话框是否能正确读取
        from ui.subtitle_translation_dialog import SubtitleTranslationDialog
        
        # 创建对话框实例（不显示UI）
        dialog = SubtitleTranslationDialog()
        print("✅ 字幕翻译对话框创建成功")
        
        # 检查配置管理器是否正确初始化
        if hasattr(dialog, 'config_manager'):
            print("✅ 配置管理器已正确初始化")
            
            # 测试从配置管理器读取密钥
            loaded_key = dialog.config_manager.get("api_keys.deepl_api_key", "")
            print(f"📥 对话框读取的密钥: {loaded_key}")
            
            if loaded_key == test_key:
                print("✅ 字幕翻译对话框配置加载成功")
                return True
            else:
                print(f"❌ 字幕翻译对话框配置加载失败，期望: {test_key}，实际: {loaded_key}")
                return False
        else:
            print("❌ 配置管理器未正确初始化")
            return False
        
    except Exception as e:
        print(f"❌ 字幕翻译对话框配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file_persistence():
    """测试配置文件持久化"""
    print("\n💾 测试配置文件持久化...")
    
    try:
        import json
        from core.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        # 保存一个测试密钥
        test_key = "persistence-test-key-abcdef"
        config_manager.update_api_key("deepl", "key", test_key)
        print(f"💾 保存测试密钥: {test_key}")
        
        # 检查配置文件
        config_file = config_manager.config_file
        print(f"📁 配置文件路径: {config_file}")
        
        if os.path.exists(config_file):
            print("✅ 配置文件存在")
            
            # 读取配置文件内容
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = json.load(f)
            
            # 检查DeepL密钥
            deepl_key = config_content.get('api_keys', {}).get('deepl_api_key', '')
            print(f"📥 配置文件中的DeepL密钥: {deepl_key}")
            
            if deepl_key == test_key:
                print("✅ 配置文件持久化成功")
                return True
            else:
                print(f"❌ 配置文件持久化失败，期望: {test_key}，实际: {deepl_key}")
                return False
        else:
            print("❌ 配置文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 配置文件持久化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI DeepL API密钥配置简单测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("配置管理器基本功能", test_config_manager),
        ("API设置组件方法", test_api_settings_methods),
        ("字幕翻译对话框配置", test_translation_dialog_config),
        ("配置文件持久化", test_config_file_persistence)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*15} {test_name} {'='*15}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🔍 测试结果汇总:")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 DeepL API密钥配置功能正常工作！")
        
        print("\n✅ 修复总结:")
        print("1. ✅ API设置界面保存功能已修复")
        print("2. ✅ API设置界面加载功能已修复")
        print("3. ✅ 字幕翻译对话框配置加载已修复")
        print("4. ✅ 配置文件持久化正常工作")
        
        print("\n🎯 使用流程:")
        print("1. 在API设置界面输入DeepL API密钥")
        print("2. 点击保存按钮保存到配置文件")
        print("3. 打开字幕翻译对话框")
        print("4. 选择DeepL翻译器，密钥自动填充")
        print("5. 开始翻译字幕")
        
        print("\n🔧 技术实现:")
        print("• API设置界面连接到config_manager.update_api_key")
        print("• 字幕翻译对话框从config_manager.get读取密钥")
        print("• 配置持久化到JSON文件")
        print("• 翻译器切换时自动加载已保存的密钥")
        
    else:
        print(f"\n❌ 仍有 {total_tests - passed_tests} 个问题需要修复")
        
        print("\n🔧 可能的问题:")
        print("• 配置管理器初始化失败")
        print("• 文件权限问题")
        print("• 模块导入问题")
        print("• 配置文件路径问题")

if __name__ == "__main__":
    main()
